# PowerShell script to fix incomplete import statements
# Fixes imports that are missing the 'from' clause

Write-Host "Starting to fix incomplete import statements..." -ForegroundColor Green

$totalFiles = 0
$fixedFiles = 0

# Common import mappings based on typical patterns
$importMappings = @{
    'PipelineCache' = "'../cache/pipeline_cache'"
    'WorkflowCallbacks' = "'../callbacks/workflow_callbacks'"
    'GraphRagConfig' = "'../config/models/graph_rag_config'"
    'InputConfig' = "'../config/models/input_config'"
    'PipelineStorage' = "'../storage/pipeline_storage'"
    'getSummarizedEntitiesRelationships' = "'../operations/summarize_entities_relationships'"
    'getDeltaDocs' = "'../operations/get_delta_docs'"
}

# Get all TypeScript and JavaScript files
$files = Get-ChildItem -Path . -Recurse -Include "*.ts", "*.js", "*.tsx", "*.jsx"

foreach ($file in $files) {
    $totalFiles++
    $lines = Get-Content $file.FullName -Encoding UTF8
    $changed = $false
    $fixedLines = @()
    
    for ($i = 0; $i -lt $lines.Length; $i++) {
        $line = $lines[$i]
        
        # Check for incomplete import statements (missing 'from' clause)
        if ($line -match "^import \{ ([^}]+) \}$") {
            $importItems = $matches[1].Trim()
            
            # Try to find a mapping for this import
            $found = $false
            foreach ($key in $importMappings.Keys) {
                if ($importItems -eq $key -or $importItems.Contains($key)) {
                    $fixedLines += "import { $importItems } from $($importMappings[$key]);"
                    $changed = $true
                    $found = $true
                    Write-Host "  Fixed import: $importItems -> $($importMappings[$key])" -ForegroundColor Green
                    break
                }
            }
            
            if (-not $found) {
                # Keep as FIXME comment for manual review
                $fixedLines += "// FIXME: Incomplete import needs manual fix: import { $importItems } from '???';"
                $changed = $true
                Write-Host "  Marked for manual fix: $importItems" -ForegroundColor Yellow
            }
        }
        else {
            $fixedLines += $line
        }
    }
    
    # Write back to file if changed
    if ($changed) {
        $fixedLines | Set-Content -Path $file.FullName -Encoding UTF8
        $fixedFiles++
        Write-Host "Fixed incomplete imports in: $($file.FullName)" -ForegroundColor Cyan
    }
    
    # Show progress every 50 files
    if ($totalFiles % 50 -eq 0) {
        Write-Host "Processed $totalFiles files, fixed $fixedFiles files" -ForegroundColor Gray
    }
}

Write-Host ""
Write-Host "Incomplete import fix complete!" -ForegroundColor Green
Write-Host "Total files processed: $totalFiles" -ForegroundColor White
Write-Host "Files with incomplete imports fixed: $fixedFiles" -ForegroundColor White
