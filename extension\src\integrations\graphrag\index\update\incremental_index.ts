/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Dataframe operations and utils for Incremental Indexing.
 */

import { DataFrame } from '../../data_model/types';
import { PipelineStorage } from '../../storage/pipeline_storage';
import { loadTableFromStorage, writeTableToStorage } from '../../utils/storage';

/**
 * Dataclass to hold the input delta.
 */
export interface InputDelta {
    /** The new inputs */
    newInputs: DataFrame;
    /** The deleted inputs */
    deletedInputs: DataFrame;
}

/**
 * Get the delta between the input dataset and the final documents.
 * @param inputDataset - The input dataset
 * @param storage - The Pipeline storage
 * @returns The input delta with new inputs and deleted inputs
 */
export async function getDeltaDocs(
    inputDataset: DataFrame,
    storage: PipelineStorage
): Promise<InputDelta> {
    const finalDocs = await loadTableFromStorage("documents", storage);

    // Get unique titles from both datasets
    const previousDocs = Array.from(new Set(
        finalDocs.data.map(row => row.title).filter(title => title != null)
    ));
    const datasetDocs = Array.from(new Set(
        inputDataset.data.map(row => row.title).filter(title => title != null)
    ));

    // Get new documents (in input dataset but not in previous docs)
    const newDocsData = inputDataset.data.filter(row => 
        row.title != null && !previousDocs.includes(row.title)
    );

    // Get deleted documents (in previous docs but not in input dataset)
    const deletedDocsData = finalDocs.data.filter(row => 
        row.title != null && !datasetDocs.includes(row.title)
    );

    return {
        newInputs: {
            columns: inputDataset.columns,
            data: newDocsData
        },
        deletedInputs: {
            columns: finalDocs.columns,
            data: deletedDocsData
        }
    };
}

/**
 * Concatenate dataframes.
 * @param name - Table name
 * @param previousStorage - Previous storage
 * @param deltaStorage - Delta storage
 * @param outputStorage - Output storage
 * @returns Concatenated DataFrame
 */
export async function concatDataframes(
    name: string,
    previousStorage: PipelineStorage,
    deltaStorage: PipelineStorage,
    outputStorage: PipelineStorage
): Promise<DataFrame> {
    const oldDF = await loadTableFromStorage(name, previousStorage);
    const deltaDF = await loadTableFromStorage(name, deltaStorage);

    // Find max human readable ID from old DataFrame
    const maxHumanReadableId = Math.max(
        ...oldDF.data.map(row => parseInt(String(row.human_readable_id), 10))
    );

    // Update delta DataFrame with new human readable IDs
    const updatedDeltaData = deltaDF.data.map((row, index) => ({
        ...row,
        human_readable_id: maxHumanReadableId + 1 + index
    }));

    // Combine DataFrames
    const finalData = [...oldDF.data, ...updatedDeltaData];
    
    // Combine columns
    const allColumns = new Set([...oldDF.columns, ...deltaDF.columns]);
    
    const finalDF: DataFrame = {
        columns: Array.from(allColumns),
        data: finalData
    };

    // Write to output storage
    await writeTableToStorage(finalDF, name, outputStorage);

    return finalDF;
}