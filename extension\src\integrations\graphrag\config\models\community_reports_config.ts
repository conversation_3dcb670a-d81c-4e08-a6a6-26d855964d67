﻿// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Parameterization settings for the community reports configuration.
 */

import { readFileSync } from 'fs';
import { join } from 'path';
import { graphragConfigDefaults } from '../defaults.js';
import { LanguageModelConfig } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { CreateCommunityReportsStrategyType } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;

/**
 * Configuration section for community reports.
 */
export interface CommunityReportsConfig {
  /**
   * The model ID to use for community reports.
   */
  modelId: string;

  /**
   * The community report extraction prompt to use for graph-based summarization.
   */
  graphPrompt?: string;

  /**
   * The community report extraction prompt to use for text-based summarization.
   */
  textPrompt?: string;

  /**
   * The community report maximum length in tokens.
   */
  maxLength: number;

  /**
   * The maximum input length in tokens to use when generating reports.
   */
  maxInputLength: number;

  /**
   * The override strategy to use.
   */
  strategy?: Record<string, any>;
}

/**
 * Create a CommunityReportsConfig with default values.
 */
export function createCommunityReportsConfig(config: Partial<CommunityReportsConfig> = {}): CommunityReportsConfig {
  return {
    modelId: config.modelId ?? graphragConfigDefaults.communityReports.modelId,
    graphPrompt: config.graphPrompt ?? graphragConfigDefaults.communityReports.graphPrompt,
    textPrompt: config.textPrompt ?? graphragConfigDefaults.communityReports.textPrompt,
    maxLength: config.maxLength ?? graphragConfigDefaults.communityReports.maxLength,
    maxInputLength: config.maxInputLength ?? graphragConfigDefaults.communityReports.maxInputLength,
    strategy: config.strategy ?? graphragConfigDefaults.communityReports.strategy,
  };
}

/**
 * Get the resolved community report extraction strategy.
 */
export function getResolvedCommunityReportsStrategy(
  config: CommunityReportsConfig,
  rootDir: string,
  modelConfig: LanguageModelConfig
): Record<string, any> {
  return config.strategy || {
    type: CreateCommunityReportsStrategyType.GraphIntelligence,
    llm: modelConfig,
    graphPrompt: config.graphPrompt
      ? readFileSync(join(rootDir, config.graphPrompt), 'utf-8')
      : undefined,
    textPrompt: config.textPrompt
      ? readFileSync(join(rootDir, config.textPrompt), 'utf-8')
      : undefined,
    maxReportLength: config.maxLength,
    maxInputLength: config.maxInputLength,
  };
}
