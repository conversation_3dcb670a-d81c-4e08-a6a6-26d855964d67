﻿/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Graph pruning.
 */

import { Graph } from '../utils/graphs';
import { stableLargestConnectedComponent }
import { NODE_FREQUENCY, EDGE_WEIGHT }

/**
 * Prune graph by removing nodes that are out of frequency/degree ranges and edges with low weights.
 * @param graph - The graph to prune
 * @param minNodeFreq - Minimum node frequency
 * @param maxNodeFreqStd - Maximum node frequency standard deviation
 * @param minNodeDegree - Minimum node degree
 * @param maxNodeDegreeStd - Maximum node degree standard deviation
 * @param minEdgeWeightPct - Minimum edge weight percentile
 * @param removeEgoNodes - Whether to remove ego nodes
 * @param lccOnly - Whether to return only largest connected component
 * @returns Pruned graph
 */
export function pruneGraph(
    graph: Graph,
    minNodeFreq: number = 1,
    maxNodeFreqStd?: number,
    minNodeDegree: number = 1,
    maxNodeDegreeStd?: number,
    minEdgeWeightPct: number = 40,
    removeEgoNodes: boolean = false,
    lccOnly: boolean = false
): Graph {
    // Create a copy of the graph
    const prunedGraph: Graph = {
        nodes: new Map(graph.nodes),
        edges: new Map(graph.edges)
    };

    // Calculate degrees
    const degrees = calculateDegrees(prunedGraph);

    // Remove ego nodes if needed
    if (removeEgoNodes) {
        const egoNode = findEgoNode(degrees);
        if (egoNode) {
            prunedGraph.nodes.delete(egoNode);
            removeNodeEdges(prunedGraph, egoNode);
        }
    }

    // Remove nodes with degree below minimum
    const nodesToRemoveByDegree = Array.from(degrees.entries())
        .filter(([_, degree]) => degree < minNodeDegree)
        .map(([node, _]) => node);
    
    nodesToRemoveByDegree.forEach(node => {
        prunedGraph.nodes.delete(node);
        removeNodeEdges(prunedGraph, node);
    });

    // Remove nodes with degree above threshold (if specified)
    if (maxNodeDegreeStd !== undefined) {
        const degreeValues = Array.from(degrees.values());
        const upperThreshold = getUpperThresholdByStd(degreeValues, maxNodeDegreeStd);
        
        const nodesToRemoveByMaxDegree = Array.from(degrees.entries())
            .filter(([_, degree]) => degree > upperThreshold)
            .map(([node, _]) => node);
        
        nodesToRemoveByMaxDegree.forEach(node => {
            prunedGraph.nodes.delete(node);
            removeNodeEdges(prunedGraph, node);
        });
    }

    // Remove nodes with frequency below minimum
    const nodesToRemoveByFreq: string[] = [];
    for (const [nodeId, nodeData] of prunedGraph.nodes) {
        const frequency = nodeData[NODE_FREQUENCY] || 0;
        if (frequency < minNodeFreq) {
            nodesToRemoveByFreq.push(nodeId);
        }
    }
    
    nodesToRemoveByFreq.forEach(node => {
        prunedGraph.nodes.delete(node);
        removeNodeEdges(prunedGraph, node);
    });

    // Remove nodes with frequency above threshold (if specified)
    if (maxNodeFreqStd !== undefined) {
        const frequencies = Array.from(prunedGraph.nodes.values())
            .map(nodeData => nodeData[NODE_FREQUENCY] || 0);
        const upperThreshold = getUpperThresholdByStd(frequencies, maxNodeFreqStd);
        
        const nodesToRemoveByMaxFreq: string[] = [];
        for (const [nodeId, nodeData] of prunedGraph.nodes) {
            const frequency = nodeData[NODE_FREQUENCY] || 0;
            if (frequency > upperThreshold) {
                nodesToRemoveByMaxFreq.push(nodeId);
            }
        }
        
        nodesToRemoveByMaxFreq.forEach(node => {
            prunedGraph.nodes.delete(node);
            removeNodeEdges(prunedGraph, node);
        });
    }

    // Remove edges by minimum weight
    if (minEdgeWeightPct > 0) {
        const edgeWeights = Array.from(prunedGraph.edges.values())
            .map(edge => edge.data?.[EDGE_WEIGHT] || edge.weight || 0);
        
        if (edgeWeights.length > 0) {
            const minEdgeWeight = percentile(edgeWeights, minEdgeWeightPct);
            
            const edgesToRemove: string[] = [];
            for (const [edgeKey, edge] of prunedGraph.edges) {
                const weight = edge.data?.[EDGE_WEIGHT] || edge.weight || 0;
                if (prunedGraph.nodes.has(edge.source) && 
                    prunedGraph.nodes.has(edge.target) && 
                    weight < minEdgeWeight) {
                    edgesToRemove.push(edgeKey);
                }
            }
            
            edgesToRemove.forEach(edgeKey => {
                prunedGraph.edges.delete(edgeKey);
            });
        }
    }

    if (lccOnly) {
        return stableLargestConnectedComponent(prunedGraph);
    }

    return prunedGraph;
}

/**
 * Calculate degrees for all nodes in the graph.
 */
function calculateDegrees(graph: Graph): Map<string, number> {
    const degrees = new Map<string, number>();
    
    // Initialize all nodes with degree 0
    for (const nodeId of graph.nodes.keys()) {
        degrees.set(nodeId, 0);
    }
    
    // Count degrees from edges
    for (const edge of graph.edges.values()) {
        const sourceDegree = degrees.get(edge.source) || 0;
        const targetDegree = degrees.get(edge.target) || 0;
        
        degrees.set(edge.source, sourceDegree + 1);
        degrees.set(edge.target, targetDegree + 1);
    }
    
    return degrees;
}

/**
 * Find the ego node (node with highest degree).
 */
function findEgoNode(degrees: Map<string, number>): string | null {
    let maxDegree = -1;
    let egoNode: string | null = null;
    
    for (const [node, degree] of degrees) {
        if (degree > maxDegree) {
            maxDegree = degree;
            egoNode = node;
        }
    }
    
    return egoNode;
}

/**
 * Remove all edges connected to a specific node.
 */
function removeNodeEdges(graph: Graph, nodeId: string): void {
    const edgesToRemove: string[] = [];
    
    for (const [edgeKey, edge] of graph.edges) {
        if (edge.source === nodeId || edge.target === nodeId) {
            edgesToRemove.push(edgeKey);
        }
    }
    
    edgesToRemove.forEach(edgeKey => {
        graph.edges.delete(edgeKey);
    });
}

/**
 * Get upper threshold by standard deviation.
 */
function getUpperThresholdByStd(data: number[], stdTrim: number): number {
    const mean = data.reduce((sum, val) => sum + val, 0) / data.length;
    const variance = data.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / data.length;
    const std = Math.sqrt(variance);
    return mean + stdTrim * std;
}

/**
 * Calculate percentile of an array.
 */
function percentile(arr: number[], p: number): number {
    const sorted = [...arr].sort((a, b) => a - b);
    const index = (p / 100) * (sorted.length - 1);
    const lower = Math.floor(index);
    const upper = Math.ceil(index);
    const weight = index % 1;
    
    if (upper >= sorted.length) return sorted[sorted.length - 1];
    return sorted[lower] * (1 - weight) + sorted[upper] * weight;
}
