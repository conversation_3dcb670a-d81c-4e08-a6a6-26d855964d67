﻿/**
 * Copyright (c) 2025 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing utils for fnllm.
 */

import * as defs from '../../../config/defaults';
import { LanguageModelConfig } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { WorkflowCallbacks } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { PipelineCache } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { ErrorHandlerFn } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { FNLLMCacheProvider } from './cache';

const logger = console;

export function createCache(cache: PipelineCache | null, name: string): FNLLMCacheProvider | null {
    if (cache === null) {
        return null;
    }
    return new FNLLMCacheProvider(cache).child(name);
}

export function createErrorHandler(callbacks: WorkflowCallbacks): ErrorHandlerFn {
    return function onError(
        error?: Error,
        stack?: string,
        details?: Record<string, any>
    ): void {
        logger.error(
            "Error Invoking LLM",
            error,
            { stack, details }
        );
    };
}

export interface OpenAIConfig {
    apiKey?: string;
    apiBase?: string;
    jsonStrategy: string;
    apiVersion?: string;
    organization?: string;
    maxRetries: number;
    maxRetryWait?: number;
    requestsPerMinute?: number;
    tokensPerMinute?: number;
    audience?: string;
    retryStrategy: string;
    timeout?: number;
    maxConcurrency: number;
    model: string;
    encoding?: string;
    deployment?: string;
    chatParameters: Record<string, any>;
}

export function createOpenaiConfig(config: LanguageModelConfig, azure: boolean): OpenAIConfig {
    const encodingModel = config.encodingModel;
    const jsonStrategy = config.modelSupportsJson ? "VALID" : "LOOSE";
    const chatParameters = getOpenaiModelParametersFromConfig(config);

    if (azure) {
        if (!config.apiBase) {
            throw new Error("Azure OpenAI Chat LLM requires an API base");
        }

        const audience = config.audience || defs.COGNITIVE_SERVICES_AUDIENCE;
        return {
            apiKey: config.apiKey,
            apiBase: config.apiBase,
            jsonStrategy,
            apiVersion: config.apiVersion,
            organization: config.organization,
            maxRetries: config.maxRetries,
            maxRetryWait: config.maxRetryWait,
            requestsPerMinute: config.requestsPerMinute,
            tokensPerMinute: config.tokensPerMinute,
            audience,
            retryStrategy: config.retryStrategy,
            timeout: config.requestTimeout,
            maxConcurrency: config.concurrentRequests,
            model: config.model,
            encoding: encodingModel,
            deployment: config.deploymentName,
            chatParameters,
        };
    }

    return {
        apiKey: config.apiKey,
        apiBase: config.apiBase,
        jsonStrategy,
        organization: config.organization,
        retryStrategy: config.retryStrategy,
        maxRetries: config.maxRetries,
        maxRetryWait: config.maxRetryWait,
        requestsPerMinute: config.requestsPerMinute,
        tokensPerMinute: config.tokensPerMinute,
        timeout: config.requestTimeout,
        maxConcurrency: config.concurrentRequests,
        model: config.model,
        encoding: encodingModel,
        chatParameters,
    };
}

export function isReasoningModel(model: string): boolean {
    return ["o1", "o1-mini", "o3-mini"].includes(model.toLowerCase());
}

export function getOpenaiModelParametersFromConfig(
    config: LanguageModelConfig
): Record<string, any> {
    return getOpenaiModelParametersFromDict(config);
}

export function getOpenaiModelParametersFromDict(config: Record<string, any>): Record<string, any> {
    const params: Record<string, any> = {
        n: config.n,
    };

    if (isReasoningModel(config.model)) {
        params.max_completion_tokens = config.maxCompletionTokens;
        params.reasoning_effort = config.reasoningEffort;
    } else {
        params.max_tokens = config.maxTokens;
        params.temperature = config.temperature;
        params.frequency_penalty = config.frequencyPenalty;
        params.presence_penalty = config.presencePenalty;
        params.top_p = config.topP;
    }

    if (config.responseFormat) {
        params.response_format = config.responseFormat;
    }

    return params;
}
