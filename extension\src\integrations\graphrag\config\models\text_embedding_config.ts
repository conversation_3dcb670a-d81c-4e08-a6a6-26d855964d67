﻿// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Parameterization settings for the text embedding configuration.
 */

import { graphragConfigDefaults } from '../defaults.js';
import { LanguageModelConfig }
import { TextEmbedStrategyType }

/**
 * Configuration section for text embeddings.
 */
export interface TextEmbeddingConfig {
  /**
   * The model ID to use for text embeddings.
   */
  modelId: string;

  /**
   * The vector store ID to use for text embeddings.
   */
  vectorStoreId: string;

  /**
   * The batch size to use.
   */
  batchSize: number;

  /**
   * The batch max tokens to use.
   */
  batchMaxTokens: number;

  /**
   * The specific embeddings to perform.
   */
  names: string[];

  /**
   * The override strategy to use.
   */
  strategy?: Record<string, any>;
}

/**
 * Create a TextEmbeddingConfig with default values.
 */
export function createTextEmbeddingConfig(config: Partial<TextEmbeddingConfig> = {}): TextEmbeddingConfig {
  return {
    modelId: config.modelId ?? graphragConfigDefaults.embedText.modelId,
    vectorStoreId: config.vectorStoreId ?? graphragConfigDefaults.embedText.vectorStoreId,
    batchSize: config.batchSize ?? graphragConfigDefaults.embedText.batchSize,
    batchMaxTokens: config.batchMaxTokens ?? graphragConfigDefaults.embedText.batchMaxTokens,
    names: config.names ?? graphragConfigDefaults.embedText.names,
    strategy: config.strategy ?? graphragConfigDefaults.embedText.strategy,
  };
}

/**
 * Get the resolved text embedding strategy.
 */
export function getResolvedTextEmbeddingStrategy(
  config: TextEmbeddingConfig,
  modelConfig: LanguageModelConfig
): Record<string, any> {
  return config.strategy || {
    type: TextEmbedStrategyType.OpenAI,
    llm: modelConfig,
    numThreads: modelConfig.concurrentRequests,
    batchSize: config.batchSize,
    batchMaxTokens: config.batchMaxTokens,
  };
}

/**
 * Create a TextEmbeddingConfig with default values.
 */
export function createTextEmbeddingConfig(config: Partial<TextEmbeddingConfig> = {}): TextEmbeddingConfig {
  return {
    modelId: config.modelId ?? graphragConfigDefaults.embed_text.model_id,
    vectorStoreId: config.vectorStoreId ?? graphragConfigDefaults.embed_text.vector_store_id,
    batchSize: config.batchSize ?? graphragConfigDefaults.embed_text.batch_size,
    batchMaxTokens: config.batchMaxTokens ?? graphragConfigDefaults.embed_text.batch_max_tokens,
    names: config.names ?? graphragConfigDefaults.embed_text.names,
    strategy: config.strategy ?? graphragConfigDefaults.embed_text.strategy,
  };
}
