/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Different methods to run the pipeline.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { WorkflowCallbacks } from '../../callbacks/workflow_callbacks.js';
import { GraphRagConfig } from '../../config/models/graph_rag_config.js';
import { PipelineStorage } from '../../storage/pipeline_storage.js';
import { create_cache_from_config, create_storage_from_config } from '../../utils/api.js';
import { load_table_from_storage, write_table_to_storage } from '../../utils/storage.js';
import { PipelineRunContext } from '../typing/context.js';
import { Pipeline } from '../typing/pipeline.js';
import { PipelineRunResult } from '../typing/pipeline_run_result.js';
import { create_run_context } from './utils.js';

const logger = console;

/**
 * Run all workflows using a simplified pipeline.
 * Matches the Python run_pipeline function exactly.
 */
export async function* run_pipeline(
    pipeline: Pipeline,
    config: GraphRagConfig,
    callbacks: WorkflowCallbacks,
    is_update_run: boolean = false
): AsyncGenerator<PipelineRunResult> {
    // Python: root_dir = config.root_dir
    const root_dir = config.root_dir;

    // Python: input_storage = create_storage_from_config(config.input.storage)
    const input_storage = create_storage_from_config(config.input.storage);
    // Python: output_storage = create_storage_from_config(config.output)
    const output_storage = create_storage_from_config(config.output);
    // Python: cache = create_cache_from_config(config.cache, root_dir)
    const cache = create_cache_from_config(config.cache, root_dir);

    // Python: # load existing state in case any workflows are stateful
    // Python: state_json = await output_storage.get("context.json")
    const state_json = await output_storage.get("context.json");
    // Python: state = json.loads(state_json) if state_json else {}
    let state = state_json ? JSON.parse(state_json) : {};

    let context: PipelineRunContext;

    // Python: if is_update_run:
    if (is_update_run) {
        // Python: logger.info("Running incremental indexing.")
        logger.info("Running incremental indexing.");

        // Python: update_storage = create_storage_from_config(config.update_index_output)
        const update_storage = create_storage_from_config(config.update_index_output);
        // Python: # we use this to store the new subset index, and will merge its content with the previous index
        // Python: update_timestamp = time.strftime("%Y%m%d-%H%M%S")
        const update_timestamp = new Date().toISOString().replace(/[:.]/g, '-').replace('T', '-').slice(0, 15);
        // Python: timestamped_storage = update_storage.child(update_timestamp)
        const timestamped_storage = update_storage.child(update_timestamp);
        // Python: delta_storage = timestamped_storage.child("delta")
        const delta_storage = timestamped_storage.child("delta");
        // Python: # copy the previous output to a backup folder, so we can replace it with the update
        // Python: # we'll read from this later when we merge the old and new indexes
        // Python: previous_storage = timestamped_storage.child("previous")
        const previous_storage = timestamped_storage.child("previous");
        // Python: await _copy_previous_output(output_storage, previous_storage)
        await _copy_previous_output(output_storage, previous_storage);

        // Python: state["update_timestamp"] = update_timestamp
        state["update_timestamp"] = update_timestamp;

        // Python: context = create_run_context(...)
        context = create_run_context(
            input_storage,
            delta_storage,
            previous_storage,
            cache,
            callbacks,
            undefined,
            state
        );
    } else {
        // Python: logger.info("Running standard indexing.")
        logger.info("Running standard indexing.");

        // Python: context = create_run_context(...)
        context = create_run_context(
            input_storage,
            output_storage,
            undefined,
            cache,
            callbacks,
            undefined,
            state
        );
    }

    // Python: async for table in _run_pipeline(...):
    //     yield table
    yield* _run_pipeline(pipeline, config, context);
}

// Python: async def _run_pipeline(...)
async function* _run_pipeline(
    pipeline: Pipeline,
    config: GraphRagConfig,
    context: PipelineRunContext
): AsyncGenerator<PipelineRunResult> {
    // Python: start_time = time.time()
    const start_time = Date.now() / 1000;

    // Python: last_workflow = "<startup>"
    let last_workflow = "<startup>";

    try {
        // Python: await _dump_json(context)
        await _dump_json(context);

        // Python: logger.info("Executing pipeline...")
        logger.info("Executing pipeline...");

        // Python: for name, workflow_function in pipeline.run():
        for (const [name, workflow_function] of pipeline.run()) {
            // Python: last_workflow = name
            last_workflow = name;
            // Python: context.callbacks.workflow_start(name, None)
            context.callbacks.workflow_start(name, null);
            // Python: work_time = time.time()
            const work_time = Date.now() / 1000;

            // Python: result = await workflow_function(config, context)
            const result = await workflow_function(config, context);

            // Python: context.callbacks.workflow_end(name, result)
            context.callbacks.workflow_end(name, result);

            // Python: yield PipelineRunResult(...)
            yield {
                workflow: name,
                result: result.result,
                state: context.state,
                errors: null
            } as PipelineRunResult;

            // Python: context.stats.workflows[name] = {"overall": time.time() - work_time}
            context.stats.workflows[name] = { overall: Date.now() / 1000 - work_time };

            // Python: if result.stop:
            if (result.stop) {
                // Python: logger.info("Halting pipeline at workflow request")
                logger.info("Halting pipeline at workflow request");
                // Python: break
                break;
            }
        }

        // Python: context.stats.total_runtime = time.time() - start_time
        context.stats.total_runtime = Date.now() / 1000 - start_time;
        // Python: logger.info("Indexing pipeline complete.")
        logger.info("Indexing pipeline complete.");
        // Python: await _dump_json(context)
        await _dump_json(context);

    } catch (e) {
        // Python: logger.exception("error running workflow %s", last_workflow)
        const error = e instanceof Error ? e : new Error(String(e));
        logger.error(`error running workflow ${last_workflow}`, error);

        // Python: yield PipelineRunResult(...)
        yield {
            workflow: last_workflow,
            result: null,
            state: context.state,
            errors: [error]
        } as PipelineRunResult;
    }
}

// Python: async def _dump_json(context: PipelineRunContext) -> None:
async function _dump_json(context: PipelineRunContext): Promise<void> {
    // Python: """Dump the stats and context state to the storage."""
    // Python: await context.output_storage.set(
    //     "stats.json", json.dumps(asdict(context.stats), indent=4, ensure_ascii=False)
    // )
    await context.output_storage.set(
        "stats.json",
        JSON.stringify(context.stats, null, 4)
    );
    // Python: await context.output_storage.set(
    //     "context.json", json.dumps(context.state, indent=4, ensure_ascii=False)
    // )
    await context.output_storage.set(
        "context.json",
        JSON.stringify(context.state, null, 4)
    );
}

// Python: async def _copy_previous_output(...)
async function _copy_previous_output(
    storage: PipelineStorage,
    copy_storage: PipelineStorage
): Promise<void> {
    // Python: for file in storage.find(re.compile(r"\.parquet$")):
    const parquet_pattern = /\.parquet$/;

    for (const [file] of storage.find(parquet_pattern)) {
        // Python: base_name = file[0].replace(".parquet", "")
        const base_name = file.replace(".parquet", "");
        // Python: table = await load_table_from_storage(base_name, storage)
        const table = await load_table_from_storage(base_name, storage);
        // Python: await write_table_to_storage(table, base_name, copy_storage)
        await write_table_to_storage(table, base_name, copy_storage);
    }
}

// Compatibility exports for existing code
export const runPipeline = run_pipeline;
export const dumpJson = _dump_json;
export const copyPreviousOutput = _copy_previous_output;