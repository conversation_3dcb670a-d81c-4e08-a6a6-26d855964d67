﻿/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing runWorkflow method definition.
 */

import { v4 as uuidv4 } from 'uuid';
import { PipelineCache }
import { WorkflowCallbacks }
import { AsyncType } from '../../config/enums';
import { GraphRagConfig }
import { COVARIATES_FINAL_COLUMNS } from '../../data_model/schemas';
import { extractCovariates as extractor }
import { PipelineRunContext } from '../typing/context';
import { WorkflowFunctionOutput } from '../typing/workflow';
import { loadTableFromStorage, writeTableToStorage } from '../../utils/storage';

const logger = console;

export interface DataFrame {
    [key: string]: any[];
    length: number;
}

/**
 * All the steps to extract and format covariates.
 */
export async function runWorkflow(
    config: GraphRagConfig,
    context: PipelineRunContext,
): Promise<WorkflowFunctionOutput> {
    logger.info("Workflow started: extract_covariates");
    
    let output: DataFrame | null = null;
    
    if (config.extractClaims.enabled) {
        const textUnits = await loadTableFromStorage("text_units", context.outputStorage);

        const extractClaimsLlmSettings = config.getLanguageModelConfig(
            config.extractClaims.modelId
        );
        const extractionStrategy = config.extractClaims.resolvedStrategy(
            config.rootDir, 
            extractClaimsLlmSettings
        );

        const asyncMode = extractClaimsLlmSettings.asyncMode;
        const numThreads = extractClaimsLlmSettings.concurrentRequests;

        output = await extractCovariatesWorkflow({
            textUnits,
            callbacks: context.callbacks,
            cache: context.cache,
            covariateType: "claim",
            extractionStrategy,
            asyncMode,
            entityTypes: null,
            numThreads,
        });

        await writeTableToStorage(output, "covariates", context.outputStorage);
    }

    logger.info("Workflow completed: extract_covariates");
    return { result: output };
}

export interface ExtractCovariatesParams {
    textUnits: DataFrame;
    callbacks: WorkflowCallbacks;
    cache: PipelineCache;
    covariateType: string;
    extractionStrategy: Record<string, any> | null;
    asyncMode?: AsyncType;
    entityTypes?: string[] | null;
    numThreads?: number;
}

/**
 * All the steps to extract and format covariates.
 */
export async function extractCovariatesWorkflow({
    textUnits,
    callbacks,
    cache,
    covariateType,
    extractionStrategy,
    asyncMode = AsyncType.AsyncIO,
    entityTypes = null,
    numThreads = 4,
}: ExtractCovariatesParams): Promise<DataFrame> {
    // Reassign the id because it will be overwritten in the output by a covariate one
    // This also results in text_unit_id being copied to the output covariate table
    const textUnitsWithId = { ...textUnits };
    textUnitsWithId.text_unit_id = [...textUnits.id];

    const covariates = await extractor({
        input: textUnitsWithId,
        callbacks,
        cache,
        column: "text",
        covariateType,
        strategy: extractionStrategy,
        asyncMode,
        entityTypes,
        numThreads,
    });

    // Generate new IDs for covariates
    const covariateType_col = covariates.covariate_type || [];
    const ids: string[] = [];
    for (let i = 0; i < covariateType_col.length; i++) {
        ids.push(uuidv4());
    }
    covariates.id = ids;

    // Add human_readable_id as index + 1
    covariates.human_readable_id = Array.from({ length: covariates.length }, (_, i) => i + 1);

    return selectColumns(covariates, COVARIATES_FINAL_COLUMNS);
}

/**
 * Select specific columns from a DataFrame.
 */
function selectColumns(df: DataFrame, columns: string[]): DataFrame {
    const result: DataFrame = { length: df.length };
    
    for (const col of columns) {
        if (df[col] !== undefined) {
            result[col] = df[col];
        } else {
            result[col] = new Array(df.length).fill(null);
        }
    }

    return result;
}
