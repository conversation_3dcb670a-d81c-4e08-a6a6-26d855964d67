/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing the Pipeline class.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { Workflow } from './workflow.js';

/**
 * Encapsulates running workflows.
 * Matches the Python Pipeline class exactly.
 */
export class Pipeline {
    // Python: def __init__(self, workflows: list[Workflow]):
    //     self.workflows = workflows
    private workflows: Workflow[];

    constructor(workflows: Workflow[]) {
        this.workflows = workflows;
    }

    /**
     * Return a Generator over the pipeline workflows.
     * Matches the Python run method exactly.
     */
    *run(): Generator<Workflow> {
        // Python: def run(self) -> Generator[Workflow]:
        //     """Return a Generator over the pipeline workflows."""
        //     yield from self.workflows
        yield* this.workflows;
    }

    /**
     * Return the names of the workflows in the pipeline.
     * Matches the Python names method exactly.
     */
    names(): string[] {
        // Python: def names(self) -> list[str]:
        //     """Return the names of the workflows in the pipeline."""
        //     return [name for name, _ in self.workflows]
        return this.workflows.map(([name, _]) => name);
    }
}