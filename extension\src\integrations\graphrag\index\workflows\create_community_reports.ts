/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing runWorkflow method definition.
 */

import * as schemas from '../../data_model/schemas';
import { PipelineCache } from '../../cache/pipeline_cache';
import { WorkflowCallbacks } from '../../callbacks/workflow_callbacks';
import { graphragConfigDefaults } from '../../config/defaults';
import { AsyncType } from '../../config/enums';
import { GraphRagConfig } from '../../config/models/graph_rag_config';
import { finalizeCommunityReports } from '../operations/finalize_community_reports';
import { explodeCommunities }
import { 
    buildLevelContext, 
    buildLocalContext 
} from '../operations/summarize_communities/graph_context/context_builder';
import { summarizeCommunities }
import { PipelineRunContext } from '../typing/context';
import { WorkflowFunctionOutput } from '../typing/workflow';
import { 
    loadTableFromStorage, 
    storageHasTable, 
    writeTableToStorage 
} from '../../utils/storage';

const logger = console;

export interface DataFrame {
    [key: string]: any[];
    length: number;
}

export interface DataFrameRow {
    [key: string]: any;
}

/**
 * All the steps to transform community reports.
 */
export async function runWorkflow(
    config: GraphRagConfig,
    context: PipelineRunContext,
): Promise<WorkflowFunctionOutput> {
    logger.info("Workflow started: create_community_reports");
    
    const edges = await loadTableFromStorage("relationships", context.outputStorage);
    const entities = await loadTableFromStorage("entities", context.outputStorage);
    const communities = await loadTableFromStorage("communities", context.outputStorage);
    
    let claims: DataFrame | null = null;
    if (config.extractClaims.enabled && await storageHasTable("covariates", context.outputStorage)) {
        claims = await loadTableFromStorage("covariates", context.outputStorage);
    }

    const communityReportsLlmSettings = config.getLanguageModelConfig(
        config.communityReports.modelId
    );
    const asyncMode = communityReportsLlmSettings.asyncMode;
    const numThreads = communityReportsLlmSettings.concurrentRequests;
    const summarizationStrategy = config.communityReports.resolvedStrategy(
        config.rootDir, 
        communityReportsLlmSettings
    );

    const output = await createCommunityReports({
        edgesInput: edges,
        entities,
        communities,
        claimsInput: claims,
        callbacks: context.callbacks,
        cache: context.cache,
        summarizationStrategy,
        asyncMode,
        numThreads,
    });

    await writeTableToStorage(output, "community_reports", context.outputStorage);

    logger.info("Workflow completed: create_community_reports");
    return { result: output };
}

export interface CreateCommunityReportsParams {
    edgesInput: DataFrame;
    entities: DataFrame;
    communities: DataFrame;
    claimsInput: DataFrame | null;
    callbacks: WorkflowCallbacks;
    cache: PipelineCache;
    summarizationStrategy: Record<string, any>;
    asyncMode?: AsyncType;
    numThreads?: number;
}

/**
 * All the steps to transform community reports.
 */
export async function createCommunityReports({
    edgesInput,
    entities,
    communities,
    claimsInput,
    callbacks,
    cache,
    summarizationStrategy,
    asyncMode = AsyncType.AsyncIO,
    numThreads = 4,
}: CreateCommunityReportsParams): Promise<DataFrame> {
    let nodes = explodeCommunities(communities, entities);

    nodes = prepNodes(nodes);
    const edges = prepEdges(edgesInput);

    let claims: DataFrame | null = null;
    if (claimsInput !== null) {
        claims = prepClaims(claimsInput);
    }

    summarizationStrategy["extraction_prompt"] = summarizationStrategy["graph_prompt"];

    const maxInputLength = summarizationStrategy["max_input_length"] || 
        graphragConfigDefaults.communityReports.maxInputLength;

    const localContexts = buildLocalContext(
        nodes,
        edges,
        claims,
        callbacks,
        maxInputLength,
    );

    const communityReports = await summarizeCommunities({
        nodes,
        communities,
        localContexts,
        levelContextBuilder: buildLevelContext,
        callbacks,
        cache,
        summarizationStrategy,
        maxInputLength,
        asyncMode,
        numThreads,
    });

    return finalizeCommunityReports(communityReports, communities);
}

/**
 * Prepare nodes by filtering, filling missing descriptions, and creating NODE_DETAILS.
 */
function prepNodes(input: DataFrame): DataFrame {
    // Fill missing values in DESCRIPTION
    const description = input[schemas.DESCRIPTION] || [];
    for (let i = 0; i < description.length; i++) {
        if (description[i] == null || description[i] === undefined) {
            description[i] = "No Description";
        }
    }
    input[schemas.DESCRIPTION] = description;

    // Create NODE_DETAILS column
    const nodeDetails: DataFrameRow[] = [];
    const shortId = input[schemas.SHORT_ID] || [];
    const title = input[schemas.TITLE] || [];
    const desc = input[schemas.DESCRIPTION] || [];
    const nodeDegree = input[schemas.NODE_DEGREE] || [];

    for (let i = 0; i < input.length; i++) {
        nodeDetails.push({
            [schemas.SHORT_ID]: shortId[i],
            [schemas.TITLE]: title[i],
            [schemas.DESCRIPTION]: desc[i],
            [schemas.NODE_DEGREE]: nodeDegree[i],
        });
    }
    input[schemas.NODE_DETAILS] = nodeDetails;

    return input;
}

/**
 * Prepare edges by filling missing descriptions and creating EDGE_DETAILS.
 */
function prepEdges(input: DataFrame): DataFrame {
    // Fill missing DESCRIPTION
    const description = input[schemas.DESCRIPTION] || [];
    for (let i = 0; i < description.length; i++) {
        if (description[i] == null || description[i] === undefined) {
            description[i] = "No Description";
        }
    }
    input[schemas.DESCRIPTION] = description;

    // Create EDGE_DETAILS column
    const edgeDetails: DataFrameRow[] = [];
    const shortId = input[schemas.SHORT_ID] || [];
    const edgeSource = input[schemas.EDGE_SOURCE] || [];
    const edgeTarget = input[schemas.EDGE_TARGET] || [];
    const desc = input[schemas.DESCRIPTION] || [];
    const edgeDegree = input[schemas.EDGE_DEGREE] || [];

    for (let i = 0; i < input.length; i++) {
        edgeDetails.push({
            [schemas.SHORT_ID]: shortId[i],
            [schemas.EDGE_SOURCE]: edgeSource[i],
            [schemas.EDGE_TARGET]: edgeTarget[i],
            [schemas.DESCRIPTION]: desc[i],
            [schemas.EDGE_DEGREE]: edgeDegree[i],
        });
    }
    input[schemas.EDGE_DETAILS] = edgeDetails;

    return input;
}

/**
 * Prepare claims by filling missing descriptions and creating CLAIM_DETAILS.
 */
function prepClaims(input: DataFrame): DataFrame {
    // Fill missing DESCRIPTION
    const description = input[schemas.DESCRIPTION] || [];
    for (let i = 0; i < description.length; i++) {
        if (description[i] == null || description[i] === undefined) {
            description[i] = "No Description";
        }
    }
    input[schemas.DESCRIPTION] = description;

    // Create CLAIM_DETAILS column
    const claimDetails: DataFrameRow[] = [];
    const shortId = input[schemas.SHORT_ID] || [];
    const claimSubject = input[schemas.CLAIM_SUBJECT] || [];
    const type = input[schemas.TYPE] || [];
    const claimStatus = input[schemas.CLAIM_STATUS] || [];
    const desc = input[schemas.DESCRIPTION] || [];

    for (let i = 0; i < input.length; i++) {
        claimDetails.push({
            [schemas.SHORT_ID]: shortId[i],
            [schemas.CLAIM_SUBJECT]: claimSubject[i],
            [schemas.TYPE]: type[i],
            [schemas.CLAIM_STATUS]: claimStatus[i],
            [schemas.DESCRIPTION]: desc[i],
        });
    }
    input[schemas.CLAIM_DETAILS] = claimDetails;

    return input;
}
