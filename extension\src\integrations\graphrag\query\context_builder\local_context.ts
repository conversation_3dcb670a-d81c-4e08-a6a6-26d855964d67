﻿/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Local Context Builder.
 */

import { Covariate } from '../../data_model/covariate';
import { Entity } from '../../data_model/entity';
import { Relationship } from '../../data_model/relationship';
import {
    getCandidateCovariates,
    toCovariateDataframe,
} from '../input/retrieval/covariates';
import { toEntityDataframe } from '../input/retrieval/entities';
import {
    getCandidateRelationships,
    getEntitiesFromRelationships,
    getInNetworkRelationships,
    getOutNetworkRelationships,
    toRelationshipDataframe,
} from '../input/retrieval/relationships';
import { numTokens }

export interface DataFrame {
    [key: string]: any[];
    length: number;
}

export function buildEntityContext(options: {
    selectedEntities: Entity[];
    tokenEncoder?: any;
    maxContextTokens?: number;
    includeEntityRank?: boolean;
    rankDescription?: string;
    columnDelimiter?: string;
    contextName?: string;
}): [string, DataFrame] {
    const {
        selectedEntities,
        tokenEncoder,
        maxContextTokens = 8000,
        includeEntityRank = true,
        rankDescription = "number of relationships",
        columnDelimiter = "|",
        contextName = "Entities"
    } = options;

    if (selectedEntities.length === 0) {
        return ["", { length: 0 }];
    }

    // Add headers
    let currentContextText = `-----${contextName}-----\n`;
    const header = ["id", "entity", "description"];
    if (includeEntityRank) {
        header.push(rankDescription);
    }

    const attributeCols = selectedEntities[0].attributes 
        ? Object.keys(selectedEntities[0].attributes) 
        : [];
    header.push(...attributeCols);
    currentContextText += header.join(columnDelimiter) + "\n";
    let currentTokens = numTokens(currentContextText, tokenEncoder);

    const allContextRecords = [header];
    for (const entity of selectedEntities) {
        const newContext = [
            entity.shortId || "",
            entity.title,
            entity.description || "",
        ];

        if (includeEntityRank) {
            newContext.push(String(entity.rank || ""));
        }

        for (const field of attributeCols) {
            const fieldValue = entity.attributes && entity.attributes[field]
                ? String(entity.attributes[field])
                : "";
            newContext.push(fieldValue);
        }

        const newContextText = newContext.join(columnDelimiter) + "\n";
        const newTokens = numTokens(newContextText, tokenEncoder);
        
        if (currentTokens + newTokens > maxContextTokens) {
            break;
        }
        
        currentContextText += newContextText;
        allContextRecords.push(newContext);
        currentTokens += newTokens;
    }

    let recordDF: DataFrame;
    if (allContextRecords.length > 1) {
        recordDF = { length: allContextRecords.length - 1 };
        for (let i = 0; i < header.length; i++) {
            const columnName = header[i];
            recordDF[columnName] = allContextRecords.slice(1).map(record => record[i]);
        }
    } else {
        recordDF = { length: 0 };
    }

    return [currentContextText, recordDF];
}

export function buildCovariatesContext(options: {
    selectedEntities: Entity[];
    covariates: Covariate[];
    tokenEncoder?: any;
    maxContextTokens?: number;
    columnDelimiter?: string;
    contextName?: string;
}): [string, DataFrame] {
    const {
        selectedEntities,
        covariates,
        tokenEncoder,
        maxContextTokens = 8000,
        columnDelimiter = "|",
        contextName = "Covariates"
    } = options;

    if (selectedEntities.length === 0 || covariates.length === 0) {
        return ["", { length: 0 }];
    }

    const selectedCovariates: Covariate[] = [];
    let recordDF: DataFrame = { length: 0 };

    // Add context header
    let currentContextText = `-----${contextName}-----\n`;

    // Add header
    const header = ["id", "entity"];
    const attributes = covariates.length > 0 && covariates[0].attributes 
        ? covariates[0].attributes 
        : {};
    const attributeCols = Object.keys(attributes);
    header.push(...attributeCols);
    currentContextText += header.join(columnDelimiter) + "\n";
    let currentTokens = numTokens(currentContextText, tokenEncoder);

    const allContextRecords = [header];
    for (const entity of selectedEntities) {
        selectedCovariates.push(
            ...covariates.filter(cov => cov.subjectId === entity.title)
        );
    }

    for (const covariate of selectedCovariates) {
        const newContext = [
            covariate.shortId || "",
            covariate.subjectId,
        ];

        for (const field of attributeCols) {
            const fieldValue = covariate.attributes && covariate.attributes[field]
                ? String(covariate.attributes[field])
                : "";
            newContext.push(fieldValue);
        }

        const newContextText = newContext.join(columnDelimiter) + "\n";
        const newTokens = numTokens(newContextText, tokenEncoder);
        
        if (currentTokens + newTokens > maxContextTokens) {
            break;
        }
        
        currentContextText += newContextText;
        allContextRecords.push(newContext);
        currentTokens += newTokens;

        if (allContextRecords.length > 1) {
            recordDF = { length: allContextRecords.length - 1 };
            for (let i = 0; i < header.length; i++) {
                const columnName = header[i];
                recordDF[columnName] = allContextRecords.slice(1).map(record => record[i]);
            }
        } else {
            recordDF = { length: 0 };
        }
    }

    return [currentContextText, recordDF];
}

export function buildRelationshipContext(options: {
    selectedEntities: Entity[];
    relationships: Relationship[];
    tokenEncoder?: any;
    includeRelationshipWeight?: boolean;
    maxContextTokens?: number;
    topKRelationships?: number;
    relationshipRankingAttribute?: string;
    columnDelimiter?: string;
    contextName?: string;
}): [string, DataFrame] {
    const {
        selectedEntities,
        relationships,
        tokenEncoder,
        includeRelationshipWeight = false,
        maxContextTokens = 8000,
        topKRelationships = 10,
        relationshipRankingAttribute = "rank",
        columnDelimiter = "|",
        contextName = "Relationships"
    } = options;

    const selectedRelationships = filterRelationships({
        selectedEntities,
        relationships,
        topKRelationships,
        relationshipRankingAttribute,
    });

    if (selectedEntities.length === 0 || selectedRelationships.length === 0) {
        return ["", { length: 0 }];
    }

    // Add headers
    let currentContextText = `-----${contextName}-----\n`;
    const header = ["id", "source", "target", "description"];
    if (includeRelationshipWeight) {
        header.push("weight");
    }

    const attributeCols = selectedRelationships[0].attributes
        ? Object.keys(selectedRelationships[0].attributes).filter(col => !header.includes(col))
        : [];
    header.push(...attributeCols);

    currentContextText += header.join(columnDelimiter) + "\n";
    let currentTokens = numTokens(currentContextText, tokenEncoder);

    const allContextRecords = [header];
    for (const rel of selectedRelationships) {
        const newContext = [
            rel.shortId || "",
            rel.source,
            rel.target,
            rel.description || "",
        ];

        if (includeRelationshipWeight) {
            newContext.push(String(rel.weight || ""));
        }

        for (const field of attributeCols) {
            const fieldValue = rel.attributes && rel.attributes[field]
                ? String(rel.attributes[field])
                : "";
            newContext.push(fieldValue);
        }

        const newContextText = newContext.join(columnDelimiter) + "\n";
        const newTokens = numTokens(newContextText, tokenEncoder);
        
        if (currentTokens + newTokens > maxContextTokens) {
            break;
        }
        
        currentContextText += newContextText;
        allContextRecords.push(newContext);
        currentTokens += newTokens;
    }

    let recordDF: DataFrame;
    if (allContextRecords.length > 1) {
        recordDF = { length: allContextRecords.length - 1 };
        for (let i = 0; i < header.length; i++) {
            const columnName = header[i];
            recordDF[columnName] = allContextRecords.slice(1).map(record => record[i]);
        }
    } else {
        recordDF = { length: 0 };
    }

    return [currentContextText, recordDF];
}

function filterRelationships(options: {
    selectedEntities: Entity[];
    relationships: Relationship[];
    topKRelationships: number;
    relationshipRankingAttribute: string;
}): Relationship[] {
    const {
        selectedEntities,
        relationships,
        topKRelationships,
        relationshipRankingAttribute
    } = options;

    // First priority: in-network relationships
    const inNetworkRelationships = getInNetworkRelationships({
        selectedEntities,
        relationships,
        rankingAttribute: relationshipRankingAttribute,
    });

    // Second priority: out-of-network relationships
    const outNetworkRelationships = getOutNetworkRelationships({
        selectedEntities,
        relationships,
        rankingAttribute: relationshipRankingAttribute,
    });

    if (outNetworkRelationships.length <= 1) {
        return [...inNetworkRelationships, ...outNetworkRelationships];
    }

    // Within out-of-network relationships, prioritize mutual relationships
    const selectedEntityNames = selectedEntities.map(entity => entity.title);
    const outNetworkSourceNames = outNetworkRelationships
        .map(rel => rel.source)
        .filter(source => !selectedEntityNames.includes(source));
    const outNetworkTargetNames = outNetworkRelationships
        .map(rel => rel.target)
        .filter(target => !selectedEntityNames.includes(target));
    
    const outNetworkEntityNames = [...new Set([...outNetworkSourceNames, ...outNetworkTargetNames])];
    const outNetworkEntityLinks: Record<string, number> = {};

    for (const entityName of outNetworkEntityNames) {
        const targets = outNetworkRelationships
            .filter(rel => rel.source === entityName)
            .map(rel => rel.target);
        const sources = outNetworkRelationships
            .filter(rel => rel.target === entityName)
            .map(rel => rel.source);
        outNetworkEntityLinks[entityName] = new Set([...targets, ...sources]).size;
    }

    // Sort out-network relationships by number of links and ranking attribute
    for (const rel of outNetworkRelationships) {
        if (!rel.attributes) {
            rel.attributes = {};
        }
        rel.attributes.links = outNetworkEntityLinks[rel.source] || outNetworkEntityLinks[rel.target] || 0;
    }

    // Sort by attributes[links] first, then by ranking attribute
    outNetworkRelationships.sort((a, b) => {
        const linksA = a.attributes?.links || 0;
        const linksB = b.attributes?.links || 0;
        if (linksA !== linksB) {
            return linksB - linksA;
        }

        if (relationshipRankingAttribute === "rank") {
            return (b.rank || 0) - (a.rank || 0);
        } else if (relationshipRankingAttribute === "weight") {
            return (b.weight || 0) - (a.weight || 0);
        } else {
            const valueA = a.attributes?.[relationshipRankingAttribute] || 0;
            const valueB = b.attributes?.[relationshipRankingAttribute] || 0;
            return valueB - valueA;
        }
    });

    const relationshipBudget = topKRelationships * selectedEntities.length;
    return [...inNetworkRelationships, ...outNetworkRelationships.slice(0, relationshipBudget)];
}

export function getCandidateContext(options: {
    selectedEntities: Entity[];
    entities: Entity[];
    relationships: Relationship[];
    covariates: Record<string, Covariate[]>;
    includeEntityRank?: boolean;
    entityRankDescription?: string;
    includeRelationshipWeight?: boolean;
}): Record<string, DataFrame> {
    const {
        selectedEntities,
        entities,
        relationships,
        covariates,
        includeEntityRank = true,
        entityRankDescription = "number of relationships",
        includeRelationshipWeight = false
    } = options;

    const candidateContext: Record<string, DataFrame> = {};
    
    const candidateRelationships = getCandidateRelationships({
        selectedEntities,
        relationships,
    });
    
    candidateContext.relationships = toRelationshipDataframe({
        relationships: candidateRelationships,
        includeRelationshipWeight,
    });

    const candidateEntities = getEntitiesFromRelationships({
        relationships: candidateRelationships,
        entities,
    });
    
    candidateContext.entities = toEntityDataframe({
        entities: candidateEntities,
        includeEntityRank,
        rankDescription: entityRankDescription,
    });

    for (const [covariateType, covariateList] of Object.entries(covariates)) {
        const candidateCovariates = getCandidateCovariates({
            selectedEntities,
            covariates: covariateList,
        });
        candidateContext[covariateType.toLowerCase()] = toCovariateDataframe(candidateCovariates);
    }

    return candidateContext;
}
