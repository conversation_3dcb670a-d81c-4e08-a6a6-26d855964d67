﻿/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Query Factory methods to support CLI.
 */

import { get_encoding } from 'tiktoken';
import { QueryCallbacks } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { GraphRagConfig } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { Community } from '../data_model/community';
import { CommunityReport } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { Covariate } from '../data_model/covariate';
import { Entity } from '../data_model/entity';
import { Relationship } from '../data_model/relationship';
import { TextUnit } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { ModelManager } from '../language_model/manager';
import { getOpenaiModelParametersFromConfig } from '../language_model/providers/fnllm/utils';
import { EntityVectorStoreKey } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { BasicSearchContext } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { BasicSearch } from './structured_search/basic_search/search';
import { DRIFTSearchContextBuilder } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { DRIFTSearch } from './structured_search/drift_search/search';
import { GlobalCommunityContext } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { GlobalSearch } from './structured_search/global_search/search';
import { LocalSearchMixedContext } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { LocalSearch } from './structured_search/local_search/search';
import { BaseVectorStore } from '../vector_stores/base';

export function getLocalSearchEngine(
    config: GraphRagConfig,
    reports: CommunityReport[],
    textUnits: TextUnit[],
    entities: Entity[],
    relationships: Relationship[],
    covariates: Record<string, Covariate[]>,
    responseType: string,
    descriptionEmbeddingStore: BaseVectorStore,
    systemPrompt?: string,
    callbacks?: QueryCallbacks[]
): LocalSearch {
    const modelSettings = config.getLanguageModelConfig(config.localSearch.chatModelId);

    const chatModel = new ModelManager().getOrCreateChatModel({
        name: "local_search_chat",
        modelType: modelSettings.type,
        config: modelSettings,
    });

    const embeddingSettings = config.getLanguageModelConfig(
        config.localSearch.embeddingModelId
    );

    const embeddingModel = new ModelManager().getOrCreateEmbeddingModel({
        name: "local_search_embedding",
        modelType: embeddingSettings.type,
        config: embeddingSettings,
    });

    const tokenEncoder = get_encoding(modelSettings.encodingModel);
    const lsConfig = config.localSearch;
    const modelParams = getOpenaiModelParametersFromConfig(modelSettings);

    return new LocalSearch({
        model: chatModel,
        systemPrompt,
        contextBuilder: new LocalSearchMixedContext({
            communityReports: reports,
            textUnits,
            entities,
            relationships,
            covariates,
            entityTextEmbeddings: descriptionEmbeddingStore,
            embeddingVectorstoreKey: EntityVectorStoreKey.ID,
            textEmbedder: embeddingModel,
            tokenEncoder,
        }),
        tokenEncoder,
        modelParams,
        contextBuilderParams: {
            textUnitProp: lsConfig.textUnitProp,
            communityProp: lsConfig.communityProp,
            conversationHistoryMaxTurns: lsConfig.conversationHistoryMaxTurns,
            conversationHistoryUserTurnsOnly: true,
            topKMappedEntities: lsConfig.topKEntities,
            topKRelationships: lsConfig.topKRelationships,
            includeEntityRank: true,
            includeRelationshipWeight: true,
            includeCommunityRank: false,
            returnCandidateContext: false,
            embeddingVectorstoreKey: EntityVectorStoreKey.ID,
            maxContextTokens: lsConfig.maxContextTokens,
        },
        responseType,
        callbacks,
    });
}

export function getGlobalSearchEngine(
    config: GraphRagConfig,
    reports: CommunityReport[],
    entities: Entity[],
    communities: Community[],
    responseType: string,
    dynamicCommunitySelection: boolean = false,
    mapSystemPrompt?: string,
    reduceSystemPrompt?: string,
    generalKnowledgeInclusionPrompt?: string,
    callbacks?: QueryCallbacks[]
): GlobalSearch {
    const modelSettings = config.getLanguageModelConfig(
        config.globalSearch.chatModelId
    );

    const model = new ModelManager().getOrCreateChatModel({
        name: "global_search",
        modelType: modelSettings.type,
        config: modelSettings,
    });

    const modelParams = getOpenaiModelParametersFromConfig(modelSettings);
    const tokenEncoder = get_encoding(modelSettings.encodingModel);
    const gsConfig = config.globalSearch;

    let dynamicCommunitySelectionKwargs = {};
    if (dynamicCommunitySelection) {
        dynamicCommunitySelectionKwargs = {
            model,
            tokenEncoder,
            keepParent: gsConfig.dynamicSearchKeepParent,
            numRepeats: gsConfig.dynamicSearchNumRepeats,
            useSummary: gsConfig.dynamicSearchUseSummary,
            concurrentCoroutines: modelSettings.concurrentRequests,
            threshold: gsConfig.dynamicSearchThreshold,
            maxLevel: gsConfig.dynamicSearchMaxLevel,
            modelParams: { ...modelParams },
        };
    }

    return new GlobalSearch({
        model,
        mapSystemPrompt,
        reduceSystemPrompt,
        generalKnowledgeInclusionPrompt,
        contextBuilder: new GlobalCommunityContext({
            communityReports: reports,
            communities,
            entities,
            tokenEncoder,
            dynamicCommunitySelection,
            dynamicCommunitySelectionKwargs,
        }),
        tokenEncoder,
        maxDataTokens: gsConfig.dataMaxTokens,
        mapLlmParams: { ...modelParams },
        reduceLlmParams: { ...modelParams },
        mapMaxLength: gsConfig.mapMaxLength,
        reduceMaxLength: gsConfig.reduceMaxLength,
        allowGeneralKnowledge: false,
        jsonMode: false,
        contextBuilderParams: {
            useCommunitySummary: false,
            shuffleData: true,
            includeCommunityRank: true,
            minCommunityRank: 0,
            communityRankName: "rank",
            includeCommunityWeight: true,
            communityWeightName: "occurrence weight",
            normalizeCommunityWeight: true,
            maxContextTokens: gsConfig.maxContextTokens,
            contextName: "Reports",
        },
        concurrentCoroutines: modelSettings.concurrentRequests,
        responseType,
        callbacks,
    });
}

export function getDriftSearchEngine(
    config: GraphRagConfig,
    reports: CommunityReport[],
    textUnits: TextUnit[],
    entities: Entity[],
    relationships: Relationship[],
    descriptionEmbeddingStore: BaseVectorStore,
    responseType: string,
    localSystemPrompt?: string,
    reduceSystemPrompt?: string,
    callbacks?: QueryCallbacks[]
): DRIFTSearch {
    const chatModelSettings = config.getLanguageModelConfig(
        config.driftSearch.chatModelId
    );

    const chatModel = new ModelManager().getOrCreateChatModel({
        name: "drift_search_chat",
        modelType: chatModelSettings.type,
        config: chatModelSettings,
    });

    const embeddingModelSettings = config.getLanguageModelConfig(
        config.driftSearch.embeddingModelId
    );

    const embeddingModel = new ModelManager().getOrCreateEmbeddingModel({
        name: "drift_search_embedding",
        modelType: embeddingModelSettings.type,
        config: embeddingModelSettings,
    });

    const tokenEncoder = get_encoding(chatModelSettings.encodingModel);

    return new DRIFTSearch({
        model: chatModel,
        contextBuilder: new DRIFTSearchContextBuilder({
            model: chatModel,
            textEmbedder: embeddingModel,
            entities,
            relationships,
            reports,
            entityTextEmbeddings: descriptionEmbeddingStore,
            textUnits,
            localSystemPrompt,
            reduceSystemPrompt,
            config: config.driftSearch,
            responseType,
        }),
        tokenEncoder,
        callbacks,
    });
}

export function getBasicSearchEngine(
    textUnits: TextUnit[],
    textUnitEmbeddings: BaseVectorStore,
    config: GraphRagConfig,
    systemPrompt?: string,
    responseType: string = "multiple paragraphs",
    callbacks?: QueryCallbacks[]
): BasicSearch {
    const chatModelSettings = config.getLanguageModelConfig(
        config.basicSearch.chatModelId
    );

    const chatModel = new ModelManager().getOrCreateChatModel({
        name: "basic_search_chat",
        modelType: chatModelSettings.type,
        config: chatModelSettings,
    });

    const embeddingModelSettings = config.getLanguageModelConfig(
        config.basicSearch.embeddingModelId
    );

    const embeddingModel = new ModelManager().getOrCreateEmbeddingModel({
        name: "basic_search_embedding",
        modelType: embeddingModelSettings.type,
        config: embeddingModelSettings,
    });

    const tokenEncoder = get_encoding(chatModelSettings.encodingModel);
    const bsConfig = config.basicSearch;
    const modelParams = getOpenaiModelParametersFromConfig(chatModelSettings);

    return new BasicSearch({
        model: chatModel,
        systemPrompt,
        responseType,
        contextBuilder: new BasicSearchContext({
            textEmbedder: embeddingModel,
            textUnitEmbeddings,
            textUnits,
            tokenEncoder,
        }),
        tokenEncoder,
        modelParams,
        contextBuilderParams: {
            embeddingVectorstoreKey: "id",
            k: bsConfig.k,
            maxContextTokens: bsConfig.maxContextTokens,
        },
        callbacks,
    });
}
