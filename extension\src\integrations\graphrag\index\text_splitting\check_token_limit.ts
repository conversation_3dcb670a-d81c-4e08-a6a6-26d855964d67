/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Token limit method definition.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { TokenTextSplitter } from './text_splitting.js';

/**
 * Check token limit.
 * Matches the Python check_token_limit function exactly.
 */
export function check_token_limit(text: string, max_token: number): number {
    // Python: """Check token limit."""
    // Python: text_splitter = TokenTextSplitter(chunk_size=max_token, chunk_overlap=0)
    const text_splitter = new TokenTextSplitter({
        chunkSize: max_token,
        chunkOverlap: 0
    });

    // Python: docs = text_splitter.split_text(text)
    const docs = text_splitter.splitText(text);

    // Python: if len(docs) > 1:
    //     return 0
    // Python: return 1
    if (docs.length > 1) {
        return 0;
    }
    return 1;
}

// Compatibility export for existing code
export const checkTokenLimit = check_token_limit;