﻿// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Auto Templating API.
 *
 * This API provides access to the auto templating feature of graphrag, allowing external applications
 * to hook into graphrag and generate prompts from private data.
 *
 * WARNING: This API is under development and may undergo changes in future releases.
 * Backwards compatibility is not guaranteed at this time.
 */

import { NoopWorkflowCallbacks } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { graphragConfigDefaults } from '../config/defaults';
import { GraphRagConfig } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { ModelManager } from '../language_model/manager';
import { initLoggers } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { MAX_TOKEN_COUNT, PROMPT_TUNING_MODEL_ID } from '../prompt_tune/defaults';
import { generateCommunityReportRating } from '../prompt_tune/generator/community_report_rating';
import { generateCommunityReporterRole } from '../prompt_tune/generator/community_reporter_role';
import { generateDomain } from '../prompt_tune/generator/domain';
import { generateEntityRelationshipExamples } from '../prompt_tune/generator/entity_relationship';
import { generateEntityTypes } from '../prompt_tune/generator/entity_types';
import { detectLanguage } from '../prompt_tune/generator/language';
import { generatePersona } from '../prompt_tune/generator/persona';
import { loadDocsInChunks } from '../prompt_tune/loader/input';
import { ENTITY_SUMMARIZATION_PROMPT } from '../prompt_tune/template/entity_summarization';
import { COMMUNITY_REPORT_SUMMARIZATION_PROMPT } from '../prompt_tune/template/community_report_summarization';
import { GRAPH_EXTRACTION_PROMPT } from '../prompt_tune/template/extract_graph';
import { DocSelectionType } from '../prompt_tune/types';

/**
 * Create a prompt for entity summarization.
 */
function createEntitySummarizationPrompt(options: {
    persona: string;
    language: string;
}): string {
    return ENTITY_SUMMARIZATION_PROMPT
        .replace('{persona}', options.persona)
        .replace('{language}', options.language);
}

/**
 * Create a prompt for community summarization.
 */
function createCommunitySummarizationPrompt(options: {
    persona: string;
    role: string;
    reportRatingDescription: string;
    language: string;
}): string {
    return COMMUNITY_REPORT_SUMMARIZATION_PROMPT
        .replace('{persona}', options.persona)
        .replace('{role}', options.role)
        .replace('{report_rating_description}', options.reportRatingDescription)
        .replace('{language}', options.language);
}

/**
 * Create a prompt for graph extraction.
 */
function createExtractGraphPrompt(options: {
    entityTypes?: string[] | null;
    docs: string[];
    examples: string;
    language: string;
    jsonMode: boolean;
    encodingModel: string;
    maxTokenCount: number;
    minExamplesRequired: number;
}): string {
    const entityTypesStr = options.entityTypes ? options.entityTypes.join(', ') : '';

    return GRAPH_EXTRACTION_PROMPT
        .replace('{entity_types}', entityTypesStr)
        .replace('{examples}', options.examples)
        .replace('{language}', options.language);
}



/**
 * Generate indexing prompts.
 * 
 * @param config - The GraphRag configuration.
 * @param chunkSize - The chunk token size to use for input text units.
 * @param overlap - The overlap between chunks.
 * @param limit - The limit of chunks to load.
 * @param selectionMethod - The chunk selection method.
 * @param domain - The domain to map the input documents to.
 * @param language - The language to use for the prompts.
 * @param maxTokens - The maximum number of tokens to use on entity extraction prompts.
 * @param discoverEntityTypes - Generate entity types.
 * @param minExamplesRequired - The minimum number of examples required for entity extraction prompts.
 * @param nSubsetMax - The number of text chunks to embed when using auto selection method.
 * @param k - The number of documents to select when using auto selection method.
 * @returns A tuple containing: entity extraction prompt, entity summarization prompt, community summarization prompt.
 */
export async function generateIndexingPrompts(options: {
    config: GraphRagConfig;
    chunkSize?: number;
    overlap?: number;
    limit?: number;
    selectionMethod?: DocSelectionType;
    domain?: string | null;
    language?: string | null;
    maxTokens?: number;
    discoverEntityTypes?: boolean;
    minExamplesRequired?: number;
    nSubsetMax?: number;
    k?: number;
}): Promise<[string, string, string]> {
    const {
        config,
        chunkSize = graphragConfigDefaults.chunks.size,
        overlap = graphragConfigDefaults.chunks.overlap,
        limit = 15,
        selectionMethod = DocSelectionType.RANDOM,
        domain = null,
        language = null,
        maxTokens = MAX_TOKEN_COUNT,
        discoverEntityTypes = true,
        minExamplesRequired = 2,
        nSubsetMax = 300,
        k = 15
    } = options;

    initLoggers({ config });

    // Retrieve documents
    console.log("Chunking documents...");
    const docList = await loadDocsInChunks({
        config,
        selectMethod: selectionMethod,
        limit,
        chunkSize,
        overlap,
        nSubsetMax,
        k,
    });

    // Create LLM from config
    // TODO: Expose a way to specify Prompt Tuning model ID through config
    console.log("Retrieving language model configuration...");
    const defaultLlmSettings = config.getLanguageModelConfig(PROMPT_TUNING_MODEL_ID);

    console.log("Creating language model...");
    const llm = ModelManager.getInstance().registerChat(
        "prompt_tuning",
        defaultLlmSettings.type as string,
        {
            config: defaultLlmSettings,
            callbacks: new NoopWorkflowCallbacks(),
            cache: null,
        }
    );

    let actualDomain = domain;
    if (!actualDomain) {
        console.log("Generating domain...");
        actualDomain = await generateDomain(llm, docList);
    }

    let actualLanguage = language;
    if (!actualLanguage) {
        console.log("Detecting language...");
        actualLanguage = await detectLanguage(llm, docList);
    }

    console.log("Generating persona...");
    const persona = await generatePersona(llm, actualDomain);

    console.log("Generating community report ranking description...");
    const communityReportRanking = await generateCommunityReportRating(
        llm,
        actualDomain,
        persona,
        docList
    );

    let entityTypes: string[] | null = null;
    const extractGraphLlmSettings = config.getLanguageModelConfig(
        config.extract_graph.model_id
    );
    
    if (discoverEntityTypes) {
        console.log("Generating entity types...");
        const entityTypesResult = await generateEntityTypes(
            llm,
            actualDomain,
            persona,
            docList,
            undefined, // use default task
            extractGraphLlmSettings.model_supports_json || false
        );
        entityTypes = Array.isArray(entityTypesResult) ? entityTypesResult : null;
    }

    console.log("Generating entity relationship examples...");
    const examplesArray = await generateEntityRelationshipExamples(
        llm,
        persona,
        entityTypes,
        docList,
        actualLanguage,
        false // config.llm.model_supports_json should be used, but these prompts are used in non-json mode by the index engine
    );
    const examples = examplesArray.join('\n');

    console.log("Generating entity extraction prompt...");
    const extractGraphPrompt = createExtractGraphPrompt({
        entityTypes,
        docs: docList,
        examples,
        language: actualLanguage,
        jsonMode: false, // config.llm.model_supports_json should be used, but these prompts are used in non-json mode by the index engine
        encodingModel: extractGraphLlmSettings.encoding_model,
        maxTokenCount: maxTokens,
        minExamplesRequired,
    });

    console.log("Generating entity summarization prompt...");
    const entitySummarizationPrompt = createEntitySummarizationPrompt({
        persona,
        language: actualLanguage,
    });

    console.log("Generating community reporter role...");
    const communityReporterRole = await generateCommunityReporterRole(
        llm,
        actualDomain,
        persona,
        docList
    );

    console.log("Generating community summarization prompt...");
    const communitySummarizationPrompt = createCommunitySummarizationPrompt({
        persona,
        role: communityReporterRole,
        reportRatingDescription: communityReportRanking,
        language: actualLanguage,
    });

    console.debug(`Generated domain: ${actualDomain}`);
    console.debug(`Detected language: ${actualLanguage}`);
    console.debug(`Generated persona: ${persona}`);

    return [
        extractGraphPrompt,
        entitySummarizationPrompt,
        communitySummarizationPrompt,
    ];
}
