/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Apply a generic transform function to each row in a table.
 */

import { DataFrame } from '../../data_model/types';
import { WorkflowCallbacks } from '../../callbacks/workflow_callbacks';
import { NoopWorkflowCallbacks } from '../../callbacks/noop_workflow_callbacks';
import { AsyncType } from '../../config/enums';

const logger = console;

/**
 * Exception for invalid parallel processing.
 */
export class ParallelizationError extends Error {
    constructor(numErrors: number, example?: string) {
        let message = `${numErrors} Errors occurred while running parallel transformation, could not complete!`;
        if (example) {
            message += `\nExample error: ${example}`;
        }
        super(message);
        this.name = 'ParallelizationError';
    }
}

/**
 * Apply a generic transform function to each row. Any errors will be reported and thrown.
 * @param input - Input DataFrame
 * @param transform - Transform function to apply to each row
 * @param callbacks - Workflow callbacks
 * @param numThreads - Number of concurrent operations
 * @param asyncType - Type of async processing
 * @param progressMsg - Progress message
 * @returns Array of transformed results
 */
export async function deriveFromRows<T>(
    input: DataFrame,
    transform: (row: Record<string, any>) => Promise<T>,
    callbacks?: WorkflowCallbacks,
    numThreads: number = 4,
    asyncType: AsyncType = AsyncType.AsyncIO,
    progressMsg: string = ''
): Promise<Array<T | null>> {
    const callbacksToUse = callbacks || new NoopWorkflowCallbacks();
    
    switch (asyncType) {
        case AsyncType.AsyncIO:
            return await deriveFromRowsAsyncio(
                input, transform, callbacksToUse, numThreads, progressMsg
            );
        case AsyncType.Threaded:
            return await deriveFromRowsAsyncioThreads(
                input, transform, callbacksToUse, numThreads, progressMsg
            );
        default:
            throw new Error(`Unsupported scheduling type ${asyncType}`);
    }
}

/**
 * Derive from rows asynchronously using AsyncIO.
 */
async function deriveFromRowsAsyncio<T>(
    input: DataFrame,
    transform: (row: Record<string, any>) => Promise<T>,
    callbacks: WorkflowCallbacks,
    numThreads: number = 4,
    progressMsg: string = ''
): Promise<Array<T | null>> {
    const semaphore = new Semaphore(numThreads);

    async function gather(execute: (row: Record<string, any>, index: number) => Promise<T | null>): Promise<Array<T | null>> {
        const tasks = input.data.map(async (row, index) => {
            await semaphore.acquire();
            try {
                return await execute(row, index);
            } finally {
                semaphore.release();
            }
        });
        
        return await Promise.all(tasks);
    }

    return await deriveFromRowsBase(input, transform, callbacks, gather, progressMsg);
}

/**
 * Derive from rows asynchronously using threads (simulated with Promise-based concurrency).
 */
async function deriveFromRowsAsyncioThreads<T>(
    input: DataFrame,
    transform: (row: Record<string, any>) => Promise<T>,
    callbacks: WorkflowCallbacks,
    numThreads: number = 4,
    progressMsg: string = ''
): Promise<Array<T | null>> {
    const semaphore = new Semaphore(numThreads);

    async function gather(execute: (row: Record<string, any>, index: number) => Promise<T | null>): Promise<Array<T | null>> {
        const tasks = input.data.map(async (row, index) => {
            await semaphore.acquire();
            try {
                // Simulate thread execution with setTimeout
                return await new Promise<T | null>((resolve) => {
                    setTimeout(async () => {
                        resolve(await execute(row, index));
                    }, 0);
                });
            } finally {
                semaphore.release();
            }
        });
        
        return await Promise.all(tasks);
    }

    return await deriveFromRowsBase(input, transform, callbacks, gather, progressMsg);
}

/**
 * Base implementation for derive from rows.
 */
async function deriveFromRowsBase<T>(
    input: DataFrame,
    transform: (row: Record<string, any>) => Promise<T>,
    callbacks: WorkflowCallbacks,
    gather: (execute: (row: Record<string, any>, index: number) => Promise<T | null>) => Promise<Array<T | null>>,
    progressMsg: string = ''
): Promise<Array<T | null>> {
    const errors: Array<[Error, string]> = [];
    let completed = 0;
    const total = input.data.length;

    // Simple progress ticker
    const tick = (increment: number) => {
        completed += increment;
        if (callbacks.progress) {
            callbacks.progress(completed / total, progressMsg);
        }
    };

    async function execute(row: Record<string, any>, index: number): Promise<T | null> {
        try {
            const result = await transform(row);
            return result;
        } catch (e) {
            const error = e instanceof Error ? e : new Error(String(e));
            errors.push([error, error.stack || '']);
            return null;
        } finally {
            tick(1);
        }
    }

    const result = await gather(execute);

    // Log errors
    for (const [error, stack] of errors) {
        logger.error('parallel transformation error', error, { stack });
    }

    if (errors.length > 0) {
        throw new ParallelizationError(errors.length, errors[0][1]);
    }

    return result;
}

/**
 * Simple semaphore implementation for controlling concurrency.
 */
class Semaphore {
    private permits: number;
    private waitQueue: Array<() => void> = [];

    constructor(permits: number) {
        this.permits = permits;
    }

    async acquire(): Promise<void> {
        if (this.permits > 0) {
            this.permits--;
            return;
        }

        return new Promise<void>((resolve) => {
            this.waitQueue.push(resolve);
        });
    }

    release(): void {
        if (this.waitQueue.length > 0) {
            const resolve = this.waitQueue.shift()!;
            resolve();
        } else {
            this.permits++;
        }
    }
}