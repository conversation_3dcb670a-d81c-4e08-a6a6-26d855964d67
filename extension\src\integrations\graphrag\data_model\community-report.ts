/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 * 
 * A package containing the 'CommunityReport' model.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { Named } from './named'

/**
 * Defines an LLM-generated summary report of a community.
 */
export interface CommunityReport extends Named {
    /** The ID of the community this report is associated with. */
    community_id: string

    /** Summary of the report. */
    summary: string

    /** Full content of the report. */
    full_content: string

    /** Rank of the report, used for sorting (optional). Higher means more important */
    rank?: number

    /** The semantic (i.e. text) embedding of the full report content (optional). */
    full_content_embedding?: number[]

    /** A dictionary of additional attributes associated with the report (optional). */
    attributes?: Record<string, any>

    /** The size of the report (Amount of text units). */
    size?: number

    /** The period of the report (optional). */
    period?: string
}

/**
 * Create a new community report from the dict data.
 */
export function createCommunityReportFromDict(
    d: Record<string, any>,
    options: {
        id_key?: string
        title_key?: string
        community_id_key?: string
        short_id_key?: string
        summary_key?: string
        full_content_key?: string
        rank_key?: string
        attributes_key?: string
        size_key?: string
        period_key?: string
    } = {}
): CommunityReport {
    const {
        id_key = "id",
        title_key = "title",
        community_id_key = "community",
        short_id_key = "human_readable_id",
        summary_key = "summary",
        full_content_key = "full_content",
        rank_key = "rank",
        attributes_key = "attributes",
        size_key = "size",
        period_key = "period"
    } = options

    return {
        id: d[id_key],
        title: d[title_key],
        community_id: d[community_id_key],
        short_id: d[short_id_key],
        summary: d[summary_key],
        full_content: d[full_content_key],
        rank: d[rank_key],
        attributes: d[attributes_key],
        size: d[size_key],
        period: d[period_key]
    }
}