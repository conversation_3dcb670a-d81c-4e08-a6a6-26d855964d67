// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Standard logging configuration for the graphrag package.
 * 
 * This module provides a standardized way to configure logging
 * for use within the graphrag package.
 * 
 * Usage:
 *     // Configuration should be done once at the start of your application:
 *     import { initLoggers } from './standard-logging';
 *     initLoggers({ logFile: "/path/to/app.log" });
 * 
 *     // Then throughout your code:
 *     import { getLogger } from './standard-logging';
 *     const logger = getLogger(__filename);
 * 
 *     // Use standard logging methods:
 *     logger.debug("Debug message");
 *     logger.info("Info message");
 *     logger.warning("Warning message");
 *     logger.error("Error message");
 *     logger.critical("Critical error message");
 * 
 * Notes:
 *     The logging system is hierarchical. Loggers are organized in a tree structure,
 *     with the root logger named 'graphrag'. All loggers created with names starting
 *     with 'graphrag.' will be children of this root logger. This allows for consistent
 *     configuration of all graphrag-related logs throughout the application.
 * 
 *     All progress logging now uses this standard logging system for consistency.
 */

import * as fs from 'fs';
import * as path from 'path';
import { ReportingType } from '../config/enums';
import { GraphRagConfig, ReportingConfig } from '../config/models/graph-rag-config';
import { BlobWorkflowLogger, LogLevel, LogRecord } from './blob-workflow-logger';

export enum LoggerLevel {
    DEBUG = 10,
    INFO = 20,
    WARNING = 30,
    ERROR = 40,
    CRITICAL = 50
}

export interface Logger {
    debug(message: string, ...args: any[]): void;
    info(message: string, ...args: any[]): void;
    warning(message: string, ...args: any[]): void;
    error(message: string, ...args: any[]): void;
    critical(message: string, ...args: any[]): void;
}

class StandardLogger implements Logger {
    private name: string;
    private level: LoggerLevel;
    private handlers: LogHandler[] = [];

    constructor(name: string, level: LoggerLevel = LoggerLevel.INFO) {
        this.name = name;
        this.level = level;
    }

    addHandler(handler: LogHandler): void {
        this.handlers.push(handler);
    }

    clearHandlers(): void {
        // Close file handlers properly before removing them
        for (const handler of this.handlers) {
            if (handler instanceof FileHandler) {
                handler.close();
            }
        }
        this.handlers = [];
    }

    setLevel(level: LoggerLevel): void {
        this.level = level;
    }

    private log(level: LoggerLevel, message: string, ...args: any[]): void {
        if (level < this.level) {
            return;
        }

        const formattedMessage = this.formatMessage(message, ...args);
        const record: LogRecord = {
            level: level as unknown as LogLevel,
            message: formattedMessage,
        };

        for (const handler of this.handlers) {
            handler.emit(record);
        }
    }

    private formatMessage(message: string, ...args: any[]): string {
        // Simple string formatting - replace %s, %d, etc.
        let formatted = message;
        for (const arg of args) {
            formatted = formatted.replace(/%[sd]/, String(arg));
        }
        return formatted;
    }

    debug(message: string, ...args: any[]): void {
        this.log(LoggerLevel.DEBUG, message, ...args);
    }

    info(message: string, ...args: any[]): void {
        this.log(LoggerLevel.INFO, message, ...args);
    }

    warning(message: string, ...args: any[]): void {
        this.log(LoggerLevel.WARNING, message, ...args);
    }

    error(message: string, ...args: any[]): void {
        this.log(LoggerLevel.ERROR, message, ...args);
    }

    critical(message: string, ...args: any[]): void {
        this.log(LoggerLevel.CRITICAL, message, ...args);
    }
}

abstract class LogHandler {
    protected formatter: LogFormatter;

    constructor(formatter?: LogFormatter) {
        this.formatter = formatter || new LogFormatter();
    }

    abstract emit(record: LogRecord): void;
}

class ConsoleHandler extends LogHandler {
    emit(record: LogRecord): void {
        const formatted = this.formatter.format(record);
        console.log(formatted);
    }
}

class FileHandler extends LogHandler {
    private filePath: string;
    private fileHandle?: fs.WriteStream;

    constructor(filePath: string, formatter?: LogFormatter) {
        super(formatter);
        this.filePath = filePath;
        
        // Ensure directory exists
        const dir = path.dirname(filePath);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }
        
        this.fileHandle = fs.createWriteStream(filePath, { flags: 'a' });
    }

    emit(record: LogRecord): void {
        if (this.fileHandle) {
            const formatted = this.formatter.format(record);
            this.fileHandle.write(formatted + '\n');
        }
    }

    close(): void {
        if (this.fileHandle) {
            this.fileHandle.end();
            this.fileHandle = undefined;
        }
    }
}

class LogFormatter {
    private format: string;
    private dateFormat: string;

    constructor(
        format: string = "%(asctime)s.%(msecs)04d - %(levelname)s - %(name)s - %(message)s",
        dateFormat: string = "%Y-%m-%d %H:%M:%S"
    ) {
        this.format = format;
        this.dateFormat = dateFormat;
    }

    format(record: LogRecord): string {
        const now = new Date();
        const timestamp = now.toISOString().replace('T', ' ').substring(0, 19);
        const msecs = now.getMilliseconds().toString().padStart(4, '0');
        const levelName = this.getLevelName(record.level);

        return this.format
            .replace('%(asctime)s', timestamp)
            .replace('%(msecs)04d', msecs)
            .replace('%(levelname)s', levelName)
            .replace('%(name)s', 'graphrag')
            .replace('%(message)s', record.message);
    }

    private getLevelName(level: LogLevel): string {
        switch (level) {
            case LogLevel.DEBUG: return 'DEBUG';
            case LogLevel.INFO: return 'INFO';
            case LogLevel.WARNING: return 'WARNING';
            case LogLevel.ERROR: return 'ERROR';
            case LogLevel.CRITICAL: return 'CRITICAL';
            default: return 'UNKNOWN';
        }
    }
}

// Global logger registry
const loggers = new Map<string, StandardLogger>();

/**
 * Get a logger by name.
 */
export function getLogger(name: string): Logger {
    if (!loggers.has(name)) {
        loggers.set(name, new StandardLogger(name));
    }
    return loggers.get(name)!;
}

/**
 * Initialize logging handlers for graphrag based on configuration.
 * 
 * This function merges the functionality of configure_logging() and create_pipeline_logger()
 * to provide a unified way to set up logging for the graphrag package.
 */
export function initLoggers(options: {
    config?: GraphRagConfig | null;
    rootDir?: string | null;
    verbose?: boolean;
    logFile?: string | null;
} = {}): void {
    const { config, rootDir, verbose = false, logFile } = options;

    // extract reporting config from GraphRagConfig if provided
    let reportingConfig: ReportingConfig;
    if (logFile) {
        // if log_file is provided directly, override config to use file-based logging
        const logPath = path.resolve(logFile);
        reportingConfig = {
            type: ReportingType.FILE,
            base_dir: path.dirname(logPath),
            connection_string: null,
            container_name: null,
            storage_account_blob_url: null,
        };
    } else if (config) {
        // use the reporting configuration from GraphRagConfig
        reportingConfig = config.reporting;
    } else {
        // default to file-based logging if no config provided
        reportingConfig = {
            type: ReportingType.FILE,
            base_dir: "logs",
            connection_string: null,
            container_name: null,
            storage_account_blob_url: null,
        };
    }

    const logger = getLogger("graphrag") as StandardLogger;
    const logLevel = verbose ? LoggerLevel.DEBUG : LoggerLevel.INFO;
    logger.setLevel(logLevel);

    // clear any existing handlers to avoid duplicate logs
    logger.clearHandlers();

    // create formatter with custom format
    const formatter = new LogFormatter();

    initConsoleLogger(verbose);

    // add more handlers based on configuration
    let handler: LogHandler;
    switch (reportingConfig.type) {
        case ReportingType.FILE:
            let logFilePath: string;
            if (logFile) {
                // use the specific log file provided
                logFilePath = path.resolve(logFile);
            } else {
                // use the config-based file path
                const logDir = path.join(rootDir || "", reportingConfig.base_dir || "");
                logFilePath = path.join(logDir, "logs.txt");
            }
            handler = new FileHandler(logFilePath, formatter);
            logger.addHandler(handler);
            break;
        case ReportingType.BLOB:
            // Note: This would need proper Azure SDK integration
            console.warn("Blob logging not fully implemented in TypeScript version");
            break;
        default:
            console.error(`Unknown reporting type '${reportingConfig.type}'.`);
    }
}

/**
 * Initialize a console logger if not already present.
 * 
 * This function sets up a logger that outputs log messages to STDOUT.
 */
export function initConsoleLogger(verbose: boolean = false): void {
    const logger = getLogger("graphrag") as StandardLogger;
    logger.setLevel(verbose ? LoggerLevel.DEBUG : LoggerLevel.INFO);
    
    // Add console handler
    const consoleHandler = new ConsoleHandler();
    logger.addHandler(consoleHandler);
}