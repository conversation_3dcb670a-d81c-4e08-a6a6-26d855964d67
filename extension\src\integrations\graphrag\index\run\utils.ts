/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Utility functions for the GraphRAG run module.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { InMemoryCache } from '../../cache/memory_pipeline_cache.js';
import { PipelineCache } from '../../cache/pipeline_cache.js';
import { NoopWorkflowCallbacks } from '../../callbacks/noop_workflow_callbacks.js';
import { WorkflowCallbacks } from '../../callbacks/workflow_callbacks.js';
import { WorkflowCallbacksManager } from '../../callbacks/workflow_callbacks_manager.js';
import { GraphRagConfig } from '../../config/models/graph_rag_config.js';
import { MemoryPipelineStorage } from '../../storage/memory_pipeline_storage.js';
import { PipelineStorage } from '../../storage/pipeline_storage.js';
import { create_storage_from_config } from '../../utils/api.js';
import { PipelineRunContext } from '../typing/context.js';
import { PipelineState } from '../typing/state.js';
import { PipelineRunStats } from '../typing/stats.js';

/**
 * Create the run context for the pipeline.
 * Matches the Python create_run_context function exactly.
 */
export function create_run_context(
    input_storage?: PipelineStorage | null,
    output_storage?: PipelineStorage | null,
    previous_storage?: PipelineStorage | null,
    cache?: PipelineCache | null,
    callbacks?: WorkflowCallbacks | null,
    stats?: PipelineRunStats | null,
    state?: PipelineState | null
): PipelineRunContext {
    // Python: """Create the run context for the pipeline."""
    // Python: return PipelineRunContext(...)
    return {
        input_storage: input_storage || new MemoryPipelineStorage(),
        output_storage: output_storage || new MemoryPipelineStorage(),
        previous_storage: previous_storage || new MemoryPipelineStorage(),
        cache: cache || new InMemoryCache(),
        callbacks: callbacks || new NoopWorkflowCallbacks(),
        stats: stats || new PipelineRunStats(),
        state: state || {},
    };
}

/**
 * Create a callback manager that encompasses multiple callbacks.
 * Matches the Python create_callback_chain function exactly.
 */
export function create_callback_chain(
    callbacks?: WorkflowCallbacks[] | null
): WorkflowCallbacks {
    // Python: """Create a callback manager that encompasses multiple callbacks."""
    // Python: manager = WorkflowCallbacksManager()
    const manager = new WorkflowCallbacksManager();
    // Python: for callback in callbacks or []:
    for (const callback of callbacks || []) {
        // Python: manager.register(callback)
        manager.register(callback);
    }
    // Python: return manager
    return manager;
}

/**
 * Get storage objects for the update index run.
 * Matches the Python get_update_storages function exactly.
 */
export function get_update_storages(
    config: GraphRagConfig,
    timestamp: string
): [PipelineStorage, PipelineStorage, PipelineStorage] {
    // Python: """Get storage objects for the update index run."""
    // Python: output_storage = create_storage_from_config(config.output)
    const output_storage = create_storage_from_config(config.output);
    // Python: update_storage = create_storage_from_config(config.update_index_output)
    const update_storage = create_storage_from_config(config.update_index_output);
    // Python: timestamped_storage = update_storage.child(timestamp)
    const timestamped_storage = update_storage.child(timestamp);
    // Python: delta_storage = timestamped_storage.child("delta")
    const delta_storage = timestamped_storage.child("delta");
    // Python: previous_storage = timestamped_storage.child("previous")
    const previous_storage = timestamped_storage.child("previous");

    // Python: return output_storage, previous_storage, delta_storage
    return [output_storage, previous_storage, delta_storage];
}

// Compatibility exports for existing code
export const createRunContext = create_run_context;
export const createCallbackChain = create_callback_chain;
export const getUpdateStorages = get_update_storages;