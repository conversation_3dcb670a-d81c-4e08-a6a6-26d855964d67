/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Pipeline workflow types.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { GraphRagConfig } from '../../config/models/graph_rag_config.js';
import { PipelineRunContext } from './context.js';

/**
 * Data container for Workflow function results.
 * Matches the Python WorkflowFunctionOutput dataclass exactly.
 */
export interface WorkflowFunctionOutput {
    /** The result of the workflow function. This can be anything - we use it only for logging downstream, and expect each workflow function to write official outputs to the provided storage */
    result: any | null;

    /** Flag to indicate if the workflow should stop after this function. This should only be used when continuation could cause an unstable failure */
    stop?: boolean;
}

/**
 * Workflow function type.
 * Matches the Python WorkflowFunction type exactly.
 */
export type WorkflowFunction = (
    config: GraphRagConfig,
    context: PipelineRunContext
) => Promise<WorkflowFunctionOutput>;

/**
 * Workflow tuple type - [name, function].
 * Matches the Python Workflow type exactly.
 */
export type Workflow = [string, WorkflowFunction];