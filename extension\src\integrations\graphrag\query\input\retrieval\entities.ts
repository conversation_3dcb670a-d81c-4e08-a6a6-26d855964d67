﻿// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Util functions to get entities from a collection.
 */

import { Entity } from '../../../data_model/entity';
import { DataFrame } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;

/**
 * Get entity by id.
 */
export function getEntityById(entities: Record<string, Entity>, value: string): Entity | undefined {
  let entity = entities[value];
  if (!entity && isValidUuid(value)) {
    entity = entities[value.replace(/-/g, '')];
  }
  return entity;
}

/**
 * Get entity by key.
 */
export function getEntityByKey(
  entities: Entity[],
  key: keyof Entity,
  value: string | number
): Entity | undefined {
  if (typeof value === 'string' && isValidUuid(value)) {
    const valueNoDashes = value.replace(/-/g, '');
    for (const entity of entities) {
      const entityValue = entity[key];
      if (entityValue === value || entityValue === valueNoDashes) {
        return entity;
      }
    }
  } else {
    for (const entity of entities) {
      if (entity[key] === value) {
        return entity;
      }
    }
  }
  return undefined;
}

/**
 * Get entities by name.
 */
export function getEntityByName(entities: Entity[], entityName: string): Entity[] {
  return entities.filter(entity => entity.title === entityName);
}

/**
 * Get entities by attribute.
 */
export function getEntityByAttribute(
  entities: Entity[],
  attributeName: string,
  attributeValue: any
): Entity[] {
  return entities.filter(entity =>
    entity.attributes && entity.attributes[attributeName] === attributeValue
  );
}

/**
 * Convert a list of entities to a pandas dataframe.
 */
export function toEntityDataframe(
  entities: Entity[],
  includeEntityRank: boolean = true,
  rankDescription: string = 'number of relationships'
): DataFrame {
  if (entities.length === 0) {
    return { length: 0 };
  }

  const header = ['id', 'entity', 'description'];
  if (includeEntityRank) {
    header.push(rankDescription);
  }

  const attributeCols = entities[0].attributes ? Object.keys(entities[0].attributes) : [];
  const filteredAttributeCols = attributeCols.filter(col => !header.includes(col));
  header.push(...filteredAttributeCols);

  const dataFrame: DataFrame = { length: entities.length };
  
  // Initialize columns
  header.forEach(col => {
    dataFrame[col] = [];
  });

  // Fill data
  entities.forEach(entity => {
    dataFrame.id.push(entity.shortId || '');
    dataFrame.entity.push(entity.title);
    dataFrame.description.push(entity.description || '');
    
    if (includeEntityRank) {
      dataFrame[rankDescription].push(String(entity.rank || ''));
    }

    // Add attribute columns
    filteredAttributeCols.forEach(field => {
      const value = entity.attributes?.[field];
      dataFrame[field].push(value ? String(value) : '');
    });
  });

  return dataFrame;
}

/**
 * Determine if a string is a valid UUID.
 */
export function isValidUuid(value: string): boolean {
  try {
    // UUID regex pattern
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(value);
  } catch {
    return false;
  }
}
