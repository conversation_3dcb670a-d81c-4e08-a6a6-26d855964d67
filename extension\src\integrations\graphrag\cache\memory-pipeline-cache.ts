// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * A module containing 'InMemoryCache' model.
 */

import { PipelineCache } from './pipeline-cache';

/**
 * In memory cache class definition.
 */
export class InMemoryCache extends PipelineCache {
    private cache: Map<string, any>;
    private name: string;

    constructor(name?: string) {
        super();
        this.cache = new Map();
        this.name = name || "";
    }

    /**
     * Get the value for the given key.
     * 
     * @param key - The key to get the value for.
     * @returns The value for the given key.
     */
    async get(key: string): Promise<any> {
        const cacheKey = this.createCacheKey(key);
        return this.cache.get(cacheKey);
    }

    /**
     * Set the value for the given key.
     * 
     * @param key - The key to set the value for.
     * @param value - The value to set.
     * @param debugData - Optional debug data (ignored in memory cache).
     */
    async set(key: string, value: any, debugData?: Record<string, any> | null): Promise<void> {
        const cacheKey = this.createCacheKey(key);
        this.cache.set(cacheKey, value);
    }

    /**
     * Return True if the given key exists in the storage.
     * 
     * @param key - The key to check for.
     * @returns True if the key exists in the storage, False otherwise.
     */
    async has(key: string): Promise<boolean> {
        const cacheKey = this.createCacheKey(key);
        return this.cache.has(cacheKey);
    }

    /**
     * Delete the given key from the storage.
     * 
     * @param key - The key to delete.
     */
    async delete(key: string): Promise<void> {
        const cacheKey = this.createCacheKey(key);
        this.cache.delete(cacheKey);
    }

    /**
     * Clear the storage.
     */
    async clear(): Promise<void> {
        this.cache.clear();
    }

    /**
     * Create a sub cache with the given name.
     */
    child(name: string): PipelineCache {
        return new InMemoryCache(name);
    }

    /**
     * Create a cache key for the given key.
     */
    private createCacheKey(key: string): string {
        return `${this.name}${key}`;
    }
}