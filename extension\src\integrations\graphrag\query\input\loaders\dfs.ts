﻿// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Load data from dataframes into collections of data objects.
 */

import { Community } from '../../../data_model/community';
import { CommunityReport }
import { Covariate } from '../../../data_model/covariate';
import { Entity } from '../../../data_model/entity';
import { Relationship } from '../../../data_model/relationship';
import { TextUnit }
import {
  toList,
  toOptionalDict,
  toOptionalFloat,
  toOptionalInt,
  toOptionalList,
  toOptionalStr,
  toStr,
} from './utils';

/**
 * DataFrame-like interface for TypeScript
 */
export interface DataFrame {
  [key: string]: any[];
  length: number;
}

/**
 * Reset index and convert the DataFrame to a list of dictionaries.
 * We add an 'Index' column for consistency.
 */
function prepareRecords(df: DataFrame): Record<string, any>[] {
  const records: Record<string, any>[] = [];
  const length = df.length || (Object.keys(df).length > 0 ? df[Object.keys(df)[0]].length : 0);
  
  for (let i = 0; i < length; i++) {
    const record: Record<string, any> = { Index: i };
    for (const [key, values] of Object.entries(df)) {
      if (Array.isArray(values)) {
        record[key] = values[i];
      }
    }
    records.push(record);
  }
  
  return records;
}

/**
 * Read entities from a dataframe using pre-converted records.
 */
export function readEntities(
  df: DataFrame,
  options: {
    idCol?: string;
    shortIdCol?: string;
    titleCol?: string;
    typeCol?: string;
    descriptionCol?: string;
    nameEmbeddingCol?: string;
    descriptionEmbeddingCol?: string;
    communityCol?: string;
    textUnitIdsCol?: string;
    rankCol?: string;
    attributesCols?: string[];
  } = {}
): Entity[] {
  const {
    idCol = 'id',
    shortIdCol = 'human_readable_id',
    titleCol = 'title',
    typeCol = 'type',
    descriptionCol = 'description',
    nameEmbeddingCol = 'name_embedding',
    descriptionEmbeddingCol = 'description_embedding',
    communityCol = 'community_ids',
    textUnitIdsCol = 'text_unit_ids',
    rankCol = 'degree',
    attributesCols,
  } = options;

  const records = prepareRecords(df);
  
  return records.map(row => ({
    id: toStr(row, idCol),
    shortId: shortIdCol ? toOptionalStr(row, shortIdCol) : String(row.Index),
    title: toStr(row, titleCol),
    type: toOptionalStr(row, typeCol),
    description: toOptionalStr(row, descriptionCol),
    nameEmbedding: toOptionalList(row, nameEmbeddingCol, Number),
    descriptionEmbedding: toOptionalList(row, descriptionEmbeddingCol, Number),
    communityIds: toOptionalList(row, communityCol, String),
    textUnitIds: toOptionalList(row, textUnitIdsCol),
    rank: toOptionalInt(row, rankCol),
    attributes: attributesCols 
      ? Object.fromEntries(attributesCols.map(col => [col, row[col]]))
      : undefined,
  }));
}

/**
 * Read relationships from a dataframe using pre-converted records.
 */
export function readRelationships(
  df: DataFrame,
  options: {
    idCol?: string;
    shortIdCol?: string;
    sourceCol?: string;
    targetCol?: string;
    descriptionCol?: string;
    rankCol?: string;
    descriptionEmbeddingCol?: string;
    weightCol?: string;
    textUnitIdsCol?: string;
    attributesCols?: string[];
  } = {}
): Relationship[] {
  const {
    idCol = 'id',
    shortIdCol = 'human_readable_id',
    sourceCol = 'source',
    targetCol = 'target',
    descriptionCol = 'description',
    rankCol = 'combined_degree',
    descriptionEmbeddingCol = 'description_embedding',
    weightCol = 'weight',
    textUnitIdsCol = 'text_unit_ids',
    attributesCols,
  } = options;

  const records = prepareRecords(df);
  
  return records.map(row => ({
    id: toStr(row, idCol),
    shortId: shortIdCol ? toOptionalStr(row, shortIdCol) : String(row.Index),
    source: toStr(row, sourceCol),
    target: toStr(row, targetCol),
    description: toOptionalStr(row, descriptionCol),
    descriptionEmbedding: toOptionalList(row, descriptionEmbeddingCol, Number),
    weight: toOptionalFloat(row, weightCol),
    textUnitIds: toOptionalList(row, textUnitIdsCol, String),
    rank: toOptionalInt(row, rankCol),
    attributes: attributesCols 
      ? Object.fromEntries(attributesCols.map(col => [col, row[col]]))
      : undefined,
  }));
}

/**
 * Read covariates from a dataframe using pre-converted records.
 */
export function readCovariates(
  df: DataFrame,
  options: {
    idCol?: string;
    shortIdCol?: string;
    subjectCol?: string;
    covariateTypeCol?: string;
    textUnitIdsCol?: string;
    attributesCols?: string[];
  } = {}
): Covariate[] {
  const {
    idCol = 'id',
    shortIdCol = 'human_readable_id',
    subjectCol = 'subject_id',
    covariateTypeCol = 'type',
    textUnitIdsCol = 'text_unit_ids',
    attributesCols,
  } = options;

  const records = prepareRecords(df);
  
  return records.map(row => ({
    id: toStr(row, idCol),
    shortId: shortIdCol ? toOptionalStr(row, shortIdCol) : String(row.Index),
    subjectId: toStr(row, subjectCol),
    covariateType: covariateTypeCol ? toStr(row, covariateTypeCol) : 'claim',
    textUnitIds: toOptionalList(row, textUnitIdsCol, String),
    attributes: attributesCols 
      ? Object.fromEntries(attributesCols.map(col => [col, row[col]]))
      : undefined,
  }));
}

/**
 * Read communities from a dataframe using pre-converted records.
 */
export function readCommunities(
  df: DataFrame,
  options: {
    idCol?: string;
    shortIdCol?: string;
    titleCol?: string;
    levelCol?: string;
    entitiesCol?: string;
    relationshipsCol?: string;
    textUnitsCol?: string;
    covariatesCol?: string;
    parentCol?: string;
    childrenCol?: string;
    attributesCols?: string[];
  } = {}
): Community[] {
  const {
    idCol = 'id',
    shortIdCol = 'community',
    titleCol = 'title',
    levelCol = 'level',
    entitiesCol = 'entity_ids',
    relationshipsCol = 'relationship_ids',
    textUnitsCol = 'text_unit_ids',
    covariatesCol = 'covariate_ids',
    parentCol = 'parent',
    childrenCol = 'children',
    attributesCols,
  } = options;

  const records = prepareRecords(df);
  
  return records.map(row => ({
    id: toStr(row, idCol),
    shortId: shortIdCol ? toOptionalStr(row, shortIdCol) : String(row.Index),
    title: toStr(row, titleCol),
    level: toStr(row, levelCol),
    entityIds: toOptionalList(row, entitiesCol, String),
    relationshipIds: toOptionalList(row, relationshipsCol, String),
    textUnitIds: toOptionalList(row, textUnitsCol, String),
    covariateIds: toOptionalDict(row, covariatesCol, String, String),
    parent: toStr(row, parentCol),
    children: toList(row, childrenCol),
    attributes: attributesCols 
      ? Object.fromEntries(attributesCols.map(col => [col, row[col]]))
      : undefined,
  }));
}

/**
 * Read community reports from a dataframe using pre-converted records.
 */
export function readCommunityReports(
  df: DataFrame,
  options: {
    idCol?: string;
    shortIdCol?: string;
    titleCol?: string;
    communityCol?: string;
    summaryCol?: string;
    contentCol?: string;
    rankCol?: string;
    contentEmbeddingCol?: string;
    attributesCols?: string[];
  } = {}
): CommunityReport[] {
  const {
    idCol = 'id',
    shortIdCol = 'community',
    titleCol = 'title',
    communityCol = 'community',
    summaryCol = 'summary',
    contentCol = 'full_content',
    rankCol = 'rank',
    contentEmbeddingCol = 'full_content_embedding',
    attributesCols,
  } = options;

  const records = prepareRecords(df);
  
  return records.map(row => ({
    id: toStr(row, idCol),
    shortId: shortIdCol ? toOptionalStr(row, shortIdCol) : String(row.Index),
    title: toStr(row, titleCol),
    communityId: toStr(row, communityCol),
    summary: toStr(row, summaryCol),
    fullContent: toStr(row, contentCol),
    rank: toOptionalFloat(row, rankCol),
    fullContentEmbedding: toOptionalList(row, contentEmbeddingCol, Number),
    attributes: attributesCols 
      ? Object.fromEntries(attributesCols.map(col => [col, row[col]]))
      : undefined,
  }));
}

/**
 * Read text units from a dataframe using pre-converted records.
 */
export function readTextUnits(
  df: DataFrame,
  options: {
    idCol?: string;
    textCol?: string;
    entitiesCol?: string;
    relationshipsCol?: string;
    covariatesCol?: string;
    tokensCol?: string;
    documentIdsCol?: string;
    attributesCols?: string[];
  } = {}
): TextUnit[] {
  const {
    idCol = 'id',
    textCol = 'text',
    entitiesCol = 'entity_ids',
    relationshipsCol = 'relationship_ids',
    covariatesCol = 'covariate_ids',
    tokensCol = 'n_tokens',
    documentIdsCol = 'document_ids',
    attributesCols,
  } = options;

  const records = prepareRecords(df);
  
  return records.map(row => ({
    id: toStr(row, idCol),
    shortId: String(row.Index),
    text: toStr(row, textCol),
    entityIds: toOptionalList(row, entitiesCol, String),
    relationshipIds: toOptionalList(row, relationshipsCol, String),
    covariateIds: toOptionalDict(row, covariatesCol, String, String),
    nTokens: toOptionalInt(row, tokensCol),
    documentIds: toOptionalList(row, documentIdsCol, String),
    attributes: attributesCols 
      ? Object.fromEntries(attributesCols.map(col => [col, row[col]]))
      : undefined,
  }));
}
