﻿/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * All the steps to transform final relationships.
 */

import { v4 as uuidv4 } from 'uuid';
import { DataFrame }
import { RELATIONSHIPS_FINAL_COLUMNS }
import { createGraph }
import { computeDegree }
import { computeEdgeCombinedDegree }

/**
 * All the steps to transform final relationships.
 * @param relationships - DataFrame containing relationships data
 * @returns Finalized relationships DataFrame
 */
export function finalizeRelationships(relationships: DataFrame): DataFrame {
    // Create graph and compute degrees
    const graph = createGraph(relationships, ['weight']);
    const degrees = computeDegree(graph);

    // Remove duplicates based on source and target
    const uniqueRelationships = new Map<string, any>();
    relationships.data.forEach(row => {
        const key = `${row.source}|${row.target}`;
        if (!uniqueRelationships.has(key)) {
            uniqueRelationships.set(key, row);
        }
    });

    const finalRelationshipsData = Array.from(uniqueRelationships.values());
    const finalRelationshipsDF: DataFrame = {
        columns: relationships.columns,
        data: finalRelationshipsData
    };

    // Compute combined degrees
    const combinedDegrees = computeEdgeCombinedDegree(
        finalRelationshipsDF,
        degrees,
        'title',
        'degree',
        'source',
        'target'
    );

    // Transform the data
    const transformedData = finalRelationshipsData.map((row, index) => ({
        ...row,
        combined_degree: combinedDegrees[index],
        human_readable_id: index,
        id: uuidv4()
    }));

    // Filter to final columns
    const filteredData = transformedData.map(row => {
        const newRow: Record<string, any> = {};
        RELATIONSHIPS_FINAL_COLUMNS.forEach(col => {
            if (col in row) {
                newRow[col] = row[col];
            }
        });
        return newRow;
    });

    return {
        columns: RELATIONSHIPS_FINAL_COLUMNS,
        data: filteredData
    };
}
