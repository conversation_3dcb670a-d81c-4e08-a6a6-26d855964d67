/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 * 
 * Common default configuration values.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { 
    AsyncType, 
    AuthType, 
    CacheType, 
    ChunkStrategyType, 
    InputFileType, 
    ModelType, 
    NounPhraseExtractorType, 
    ReportingType, 
    StorageType 
} from './enums'

// Constants
export const DEFAULT_OUTPUT_BASE_DIR = "output"
export const DEFAULT_CHAT_MODEL_ID = "default_chat_model"
export const DEFAULT_CHAT_MODEL_TYPE = ModelType.OPENAI_CHAT
export const DEFAULT_CHAT_MODEL = "gpt-4-turbo-preview"
export const DEFAULT_CHAT_MODEL_AUTH_TYPE = AuthType.API_KEY
export const DEFAULT_EMBEDDING_MODEL_ID = "default_embedding_model"
export const DEFAULT_EMBEDDING_MODEL_TYPE = ModelType.OPENAI_EMBEDDING
export const DEFAULT_EMBEDDING_MODEL = "text-embedding-3-small"
export const DEFAULT_EMBEDDING_MODEL_AUTH_TYPE = AuthType.API_KEY
export const DEFAULT_VECTOR_STORE_ID = "default_vector_store"

export const ENCODING_MODEL = "cl100k_base"
export const COGNITIVE_SERVICES_AUDIENCE = "https://cognitiveservices.azure.com/.default"

// English stop words (simplified version)
export const EN_STOP_WORDS = [
    "a", "an", "and", "are", "as", "at", "be", "by", "for", "from", "has", "he", 
    "in", "is", "it", "its", "of", "on", "that", "the", "to", "was", "will", "with"
]

/**
 * Default values for basic search.
 */
export interface BasicSearchDefaults {
    prompt?: string
    k: number
    max_context_tokens: number
    chat_model_id: string
    embedding_model_id: string
}

export const basicSearchDefaults: BasicSearchDefaults = {
    prompt: undefined,
    k: 10,
    max_context_tokens: 12_000,
    chat_model_id: DEFAULT_CHAT_MODEL_ID,
    embedding_model_id: DEFAULT_EMBEDDING_MODEL_ID
}

/**
 * Default values for cache.
 */
export interface CacheDefaults {
    type: CacheType
    base_dir: string
    connection_string?: string
    container_name?: string
    storage_account_blob_url?: string
    cosmosdb_account_url?: string
}

export const cacheDefaults: CacheDefaults = {
    type: CacheType.FILE,
    base_dir: "cache",
    connection_string: undefined,
    container_name: undefined,
    storage_account_blob_url: undefined,
    cosmosdb_account_url: undefined
}

/**
 * Default values for chunks.
 */
export interface ChunksDefaults {
    size: number
    overlap: number
    group_by_columns: string[]
    strategy: ChunkStrategyType
    encoding_model: string
    prepend_metadata: boolean
    chunk_size_includes_metadata: boolean
}

export const chunksDefaults: ChunksDefaults = {
    size: 1200,
    overlap: 100,
    group_by_columns: ["id"],
    strategy: ChunkStrategyType.TOKENS,
    encoding_model: "cl100k_base",
    prepend_metadata: false,
    chunk_size_includes_metadata: false
}

/**
 * Default values for cluster graph.
 */
export interface ClusterGraphDefaults {
    max_cluster_size: number
    use_lcc: boolean
    seed: number
}

export const clusterGraphDefaults: ClusterGraphDefaults = {
    max_cluster_size: 10,
    use_lcc: true,
    seed: 0xDEADBEEF
}

/**
 * Default values for community report.
 */
export interface CommunityReportDefaults {
    graph_prompt?: string
    text_prompt?: string
    max_length: number
    max_input_length: number
    strategy?: string
    model_id: string
}

export const communityReportDefaults: CommunityReportDefaults = {
    graph_prompt: undefined,
    text_prompt: undefined,
    max_length: 2000,
    max_input_length: 8000,
    strategy: undefined,
    model_id: DEFAULT_CHAT_MODEL_ID
}

/**
 * Default values for embedding graph.
 */
export interface EmbedGraphDefaults {
    enabled: boolean
    dimensions: number
    num_walks: number
    walk_length: number
    window_size: number
    iterations: number
    random_seed: number
    use_lcc: boolean
}

export const embedGraphDefaults: EmbedGraphDefaults = {
    enabled: false,
    dimensions: 1536,
    num_walks: 10,
    walk_length: 40,
    window_size: 2,
    iterations: 3,
    random_seed: 597832,
    use_lcc: true
}

/**
 * Default values for embedding text.
 */
export interface EmbedTextDefaults {
    model: string
    batch_size: number
    batch_max_tokens: number
    model_id: string
    names: string[]
    strategy?: string
    vector_store_id: string
}

export const embedTextDefaults: EmbedTextDefaults = {
    model: "text-embedding-3-small",
    batch_size: 16,
    batch_max_tokens: 8191,
    model_id: DEFAULT_EMBEDDING_MODEL_ID,
    names: [], // Will be populated with default_embeddings
    strategy: undefined,
    vector_store_id: DEFAULT_VECTOR_STORE_ID
}

/**
 * Default values for language model.
 */
export interface LanguageModelDefaults {
    api_key?: string
    auth_type: AuthType
    encoding_model: string
    max_tokens?: number
    temperature: number
    max_completion_tokens?: number
    reasoning_effort?: string
    top_p: number
    n: number
    frequency_penalty: number
    presence_penalty: number
    request_timeout: number
    api_base?: string
    api_version?: string
    deployment_name?: string
    organization?: string
    proxy?: string
    audience?: string
    model_supports_json?: boolean
    tokens_per_minute: "auto"
    requests_per_minute: "auto"
    retry_strategy: string
    max_retries: number
    max_retry_wait: number
    concurrent_requests: number
    responses?: any
    async_mode: AsyncType
}

export const languageModelDefaults: LanguageModelDefaults = {
    api_key: undefined,
    auth_type: AuthType.API_KEY,
    encoding_model: "",
    max_tokens: undefined,
    temperature: 0,
    max_completion_tokens: undefined,
    reasoning_effort: undefined,
    top_p: 1,
    n: 1,
    frequency_penalty: 0.0,
    presence_penalty: 0.0,
    request_timeout: 180.0,
    api_base: undefined,
    api_version: undefined,
    deployment_name: undefined,
    organization: undefined,
    proxy: undefined,
    audience: undefined,
    model_supports_json: undefined,
    tokens_per_minute: "auto",
    requests_per_minute: "auto",
    retry_strategy: "native",
    max_retries: 10,
    max_retry_wait: 10.0,
    concurrent_requests: 25,
    responses: undefined,
    async_mode: AsyncType.THREADED
}

/**
 * Default values for storage.
 */
export interface StorageDefaults {
    type: StorageType
    base_dir: string
    connection_string?: string
    container_name?: string
    storage_account_blob_url?: string
    cosmosdb_account_url?: string
}

export const storageDefaults: StorageDefaults = {
    type: StorageType.FILE,
    base_dir: DEFAULT_OUTPUT_BASE_DIR,
    connection_string: undefined,
    container_name: undefined,
    storage_account_blob_url: undefined,
    cosmosdb_account_url: undefined
}

/**
 * Default values for input.
 */
export interface InputDefaults {
    storage: StorageDefaults
    file_type: InputFileType
    encoding: string
    file_pattern: string
    file_filter?: string
    text_column: string
    title_column?: string
    metadata?: any
}

export const inputDefaults: InputDefaults = {
    storage: {
        ...storageDefaults,
        base_dir: "input"
    },
    file_type: InputFileType.TEXT,
    encoding: "utf-8",
    file_pattern: "",
    file_filter: undefined,
    text_column: "text",
    title_column: undefined,
    metadata: undefined
}

/**
 * Main GraphRAG configuration defaults.
 */
export interface GraphRagConfigDefaults {
    root_dir: string
    models: Record<string, any>
    reporting: any
    storage: StorageDefaults
    output: StorageDefaults
    outputs?: any
    cache: CacheDefaults
    input: InputDefaults
    embed_graph: EmbedGraphDefaults
    embed_text: EmbedTextDefaults
    chunks: ChunksDefaults
    language_model: LanguageModelDefaults
    // Add more as needed...
}

export const graphragConfigDefaults: GraphRagConfigDefaults = {
    root_dir: "",
    models: {},
    reporting: undefined, // Will be implemented later
    storage: storageDefaults,
    output: storageDefaults,
    outputs: undefined,
    cache: cacheDefaults,
    input: inputDefaults,
    embed_graph: embedGraphDefaults,
    embed_text: embedTextDefaults,
    chunks: chunksDefaults,
    language_model: languageModelDefaults
}