﻿// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Util functions to retrieve relationships from a collection.
 */

import { Entity } from '../../../data_model/entity';
import { Relationship } from '../../../data_model/relationship';
import { DataFrame } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;

/**
 * Get all directed relationships between selected entities, sorted by ranking_attribute.
 */
export function getInNetworkRelationships(
  selectedEntities: Entity[],
  relationships: Relationship[],
  rankingAttribute: string = 'rank'
): Relationship[] {
  const selectedEntityNames = selectedEntities.map(entity => entity.title);
  
  const selectedRelationships = relationships.filter(relationship =>
    selectedEntityNames.includes(relationship.source) &&
    selectedEntityNames.includes(relationship.target)
  );

  if (selectedRelationships.length <= 1) {
    return selectedRelationships;
  }

  // Sort by ranking attribute
  return sortRelationshipsByRank(selectedRelationships, rankingAttribute);
}

/**
 * Get relationships from selected entities to other entities that are not within the selected entities, sorted by ranking_attribute.
 */
export function getOutNetworkRelationships(
  selectedEntities: Entity[],
  relationships: Relationship[],
  rankingAttribute: string = 'rank'
): Relationship[] {
  const selectedEntityNames = selectedEntities.map(entity => entity.title);
  
  const sourceRelationships = relationships.filter(relationship =>
    selectedEntityNames.includes(relationship.source) &&
    !selectedEntityNames.includes(relationship.target)
  );
  
  const targetRelationships = relationships.filter(relationship =>
    selectedEntityNames.includes(relationship.target) &&
    !selectedEntityNames.includes(relationship.source)
  );
  
  const selectedRelationships = [...sourceRelationships, ...targetRelationships];
  return sortRelationshipsByRank(selectedRelationships, rankingAttribute);
}

/**
 * Get all relationships that are associated with the selected entities.
 */
export function getCandidateRelationships(
  selectedEntities: Entity[],
  relationships: Relationship[]
): Relationship[] {
  const selectedEntityNames = selectedEntities.map(entity => entity.title);
  
  return relationships.filter(relationship =>
    selectedEntityNames.includes(relationship.source) ||
    selectedEntityNames.includes(relationship.target)
  );
}

/**
 * Get all entities that are associated with the selected relationships.
 */
export function getEntitiesFromRelationships(
  relationships: Relationship[],
  entities: Entity[]
): Entity[] {
  const selectedEntityNames = [
    ...relationships.map(rel => rel.source),
    ...relationships.map(rel => rel.target)
  ];
  
  return entities.filter(entity => selectedEntityNames.includes(entity.title));
}

/**
 * Sort relationships by a ranking_attribute.
 */
export function sortRelationshipsByRank(
  relationships: Relationship[],
  rankingAttribute: string = 'rank'
): Relationship[] {
  if (relationships.length === 0) {
    return relationships;
  }

  // Sort by ranking attribute
  const attributeNames = relationships[0].attributes ? Object.keys(relationships[0].attributes) : [];
  
  if (attributeNames.includes(rankingAttribute)) {
    return relationships.sort((a, b) => {
      const aValue = a.attributes?.[rankingAttribute];
      const bValue = b.attributes?.[rankingAttribute];
      const aNum = aValue ? parseInt(String(aValue), 10) : 0;
      const bNum = bValue ? parseInt(String(bValue), 10) : 0;
      return bNum - aNum; // Descending order
    });
  } else if (rankingAttribute === 'rank') {
    return relationships.sort((a, b) => {
      const aRank = a.rank || 0;
      const bRank = b.rank || 0;
      return bRank - aRank; // Descending order
    });
  } else if (rankingAttribute === 'weight') {
    return relationships.sort((a, b) => {
      const aWeight = a.weight || 0;
      const bWeight = b.weight || 0;
      return bWeight - aWeight; // Descending order
    });
  }
  
  return relationships;
}

/**
 * Convert a list of relationships to a pandas dataframe.
 */
export function toRelationshipDataframe(
  relationships: Relationship[],
  includeRelationshipWeight: boolean = true
): DataFrame {
  if (relationships.length === 0) {
    return { length: 0 };
  }

  const header = ['id', 'source', 'target', 'description'];
  if (includeRelationshipWeight) {
    header.push('weight');
  }

  const attributeCols = relationships[0].attributes ? Object.keys(relationships[0].attributes) : [];
  const filteredAttributeCols = attributeCols.filter(col => !header.includes(col));
  header.push(...filteredAttributeCols);

  const dataFrame: DataFrame = { length: relationships.length };
  
  // Initialize columns
  header.forEach(col => {
    dataFrame[col] = [];
  });

  // Fill data
  relationships.forEach(rel => {
    dataFrame.id.push(rel.shortId || '');
    dataFrame.source.push(rel.source);
    dataFrame.target.push(rel.target);
    dataFrame.description.push(rel.description || '');
    
    if (includeRelationshipWeight) {
      dataFrame.weight.push(String(rel.weight || ''));
    }

    // Add attribute columns
    filteredAttributeCols.forEach(field => {
      const value = rel.attributes?.[field];
      dataFrame[field].push(value ? String(value) : '');
    });
  });

  return dataFrame;
}
