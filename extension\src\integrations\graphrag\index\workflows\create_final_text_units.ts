﻿/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing runWorkflow method definition.
 */

import { GraphRagConfig } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { TEXT_UNITS_FINAL_COLUMNS } from '../../data_model/schemas';
import { PipelineRunContext } from '../typing/context';
import { WorkflowFunctionOutput } from '../typing/workflow';
import { 
    loadTableFromStorage, 
    storageHasTable, 
    writeTableToStorage 
} from '../../utils/storage';

const logger = console;

export interface DataFrame {
    [key: string]: any[];
    length: number;
}

/**
 * All the steps to transform the text units.
 */
export async function runWorkflow(
    config: GraphRagConfig,
    context: PipelineRunContext,
): Promise<WorkflowFunctionOutput> {
    logger.info("Workflow started: create_final_text_units");
    
    const textUnits = await loadTableFromStorage("text_units", context.outputStorage);
    const finalEntities = await loadTableFromStorage("entities", context.outputStorage);
    const finalRelationships = await loadTableFromStorage("relationships", context.outputStorage);
    
    let finalCovariates: DataFrame | null = null;
    if (config.extractClaims.enabled && await storageHasTable("covariates", context.outputStorage)) {
        finalCovariates = await loadTableFromStorage("covariates", context.outputStorage);
    }

    const output = createFinalTextUnits(
        textUnits,
        finalEntities,
        finalRelationships,
        finalCovariates,
    );

    await writeTableToStorage(output, "text_units", context.outputStorage);

    logger.info("Workflow completed: create_final_text_units");
    return { result: output };
}

/**
 * All the steps to transform the text units.
 */
export function createFinalTextUnits(
    textUnits: DataFrame,
    finalEntities: DataFrame,
    finalRelationships: DataFrame,
    finalCovariates: DataFrame | null,
): DataFrame {
    // Select specific columns
    const selected = selectColumns(textUnits, ["id", "text", "document_ids", "n_tokens"]);
    
    // Add human_readable_id as index + 1
    selected.human_readable_id = Array.from({ length: selected.length }, (_, i) => i + 1);

    const entityJoin = processEntities(finalEntities);
    const relationshipJoin = processRelationships(finalRelationships);

    let entityJoined = joinDataFrames(selected, entityJoin);
    let relationshipJoined = joinDataFrames(entityJoined, relationshipJoin);
    let finalJoined = relationshipJoined;

    if (finalCovariates !== null) {
        const covariateJoin = processCovariates(finalCovariates);
        finalJoined = joinDataFrames(relationshipJoined, covariateJoin);
    } else {
        finalJoined.covariate_ids = new Array(finalJoined.length).fill([]);
    }

    const aggregated = groupByAndAggregateFirst(finalJoined, "id");

    return selectColumns(aggregated, TEXT_UNITS_FINAL_COLUMNS);
}

/**
 * Process entities DataFrame to create entity join table.
 */
function processEntities(df: DataFrame): DataFrame {
    const selected = selectColumns(df, ["id", "text_unit_ids"]);
    const unrolled = explodeColumn(selected, "text_unit_ids");

    return groupByAndAggregateUnique(unrolled, "text_unit_ids", "id", "entity_ids", "id");
}

/**
 * Process relationships DataFrame to create relationship join table.
 */
function processRelationships(df: DataFrame): DataFrame {
    const selected = selectColumns(df, ["id", "text_unit_ids"]);
    const unrolled = explodeColumn(selected, "text_unit_ids");

    return groupByAndAggregateUnique(unrolled, "text_unit_ids", "id", "relationship_ids", "id");
}

/**
 * Process covariates DataFrame to create covariate join table.
 */
function processCovariates(df: DataFrame): DataFrame {
    const selected = selectColumns(df, ["id", "text_unit_id"]);

    return groupByAndAggregateUnique(selected, "text_unit_id", "id", "covariate_ids", "id");
}

/**
 * Select specific columns from a DataFrame.
 */
function selectColumns(df: DataFrame, columns: string[]): DataFrame {
    const result: DataFrame = { length: df.length };
    
    for (const col of columns) {
        if (df[col] !== undefined) {
            result[col] = [...df[col]]; // Create a copy
        } else {
            result[col] = new Array(df.length).fill(null);
        }
    }

    return result;
}

/**
 * Explode a column containing arrays into multiple rows.
 */
function explodeColumn(df: DataFrame, columnName: string): DataFrame {
    const result: DataFrame = { length: 0 };
    const explodedRows: any[] = [];

    const columnData = df[columnName] || [];
    const otherColumns = Object.keys(df).filter(key => key !== columnName && key !== 'length');

    for (let i = 0; i < df.length; i++) {
        const values = columnData[i];
        if (Array.isArray(values)) {
            for (const value of values) {
                const row: any = { [columnName]: value };
                for (const col of otherColumns) {
                    row[col] = df[col][i];
                }
                explodedRows.push(row);
            }
        } else if (values != null) {
            const row: any = { [columnName]: values };
            for (const col of otherColumns) {
                row[col] = df[col][i];
            }
            explodedRows.push(row);
        }
    }

    // Convert back to DataFrame format
    result.length = explodedRows.length;
    const allColumns = [...new Set([columnName, ...otherColumns])];
    
    for (const col of allColumns) {
        result[col] = explodedRows.map(row => row[col]);
    }

    return result;
}

/**
 * Group by a column and aggregate another column with unique values.
 */
function groupByAndAggregateUnique(
    df: DataFrame,
    groupByCol: string,
    aggregateCol: string,
    resultCol: string,
    renameGroupCol: string
): DataFrame {
    const groups = new Map<any, Set<any>>();
    const groupKeys = df[groupByCol] || [];
    const aggregateValues = df[aggregateCol] || [];

    // Group values by key and use Set for uniqueness
    for (let i = 0; i < df.length; i++) {
        const key = groupKeys[i];
        const value = aggregateValues[i];
        
        if (!groups.has(key)) {
            groups.set(key, new Set());
        }
        groups.get(key)!.add(value);
    }

    // Convert groups back to DataFrame
    const result: DataFrame = { length: groups.size };
    const keys = Array.from(groups.keys());
    const uniqueValues = Array.from(groups.values()).map(set => Array.from(set));

    result[renameGroupCol] = keys;
    result[resultCol] = uniqueValues;

    return result;
}

/**
 * Group by a column and take the first value for all other columns.
 */
function groupByAndAggregateFirst(df: DataFrame, groupByCol: string): DataFrame {
    const groups = new Map<any, any>();
    const groupKeys = df[groupByCol] || [];
    const allColumns = Object.keys(df).filter(key => key !== 'length');

    // Group by key and take first occurrence
    for (let i = 0; i < df.length; i++) {
        const key = groupKeys[i];
        
        if (!groups.has(key)) {
            const row: any = {};
            for (const col of allColumns) {
                row[col] = df[col][i];
            }
            groups.set(key, row);
        }
    }

    // Convert groups back to DataFrame
    const result: DataFrame = { length: groups.size };
    const groupedRows = Array.from(groups.values());

    for (const col of allColumns) {
        result[col] = groupedRows.map(row => row[col]);
    }

    return result;
}

/**
 * Join two DataFrames on 'id' column with left join.
 */
function joinDataFrames(left: DataFrame, right: DataFrame): DataFrame {
    const result: DataFrame = { length: left.length };
    
    // Copy all left columns
    const leftColumns = Object.keys(left).filter(key => key !== 'length');
    for (const col of leftColumns) {
        result[col] = [...left[col]];
    }

    // Add right columns (excluding 'id' to avoid duplication)
    const rightColumns = Object.keys(right).filter(key => key !== 'length' && key !== 'id');
    const leftIds = left.id || [];
    const rightIds = right.id || [];

    for (const col of rightColumns) {
        const rightValues = right[col] || [];
        const joinedValues: any[] = [];

        for (let i = 0; i < left.length; i++) {
            const leftId = leftIds[i];
            let found = false;
            
            for (let j = 0; j < right.length; j++) {
                if (rightIds[j] === leftId) {
                    joinedValues.push(rightValues[j]);
                    found = true;
                    break;
                }
            }
            
            if (!found) {
                joinedValues.push(null);
            }
        }
        
        result[col] = joinedValues;
    }

    return result;
}
