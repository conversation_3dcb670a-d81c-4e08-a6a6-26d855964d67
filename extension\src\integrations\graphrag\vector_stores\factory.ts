﻿// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * A package containing a factory and supported vector store types.
 */

import { AzureAISearchVectorStore } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { BaseVectorStore } from './base';
import { CosmosDBVectorStore } from './cosmosdb';
import { LanceDBVectorStore } from './lancedb';

/**
 * The supported vector store types.
 */
export enum VectorStoreType {
    LanceDB = "lancedb",
    AzureAISearch = "azure_ai_search",
    CosmosDB = "cosmosdb"
}

/**
 * A factory for vector stores.
 * Includes a method for users to register a custom vector store implementation.
 */
export class VectorStoreFactory {
    private static vectorStoreTypes: Map<string, new (...args: any[]) => BaseVectorStore> = new Map();

    /**
     * Register a custom vector store implementation.
     */
    static register(vectorStoreType: string, vectorStore: new (...args: any[]) => BaseVectorStore): void {
        this.vectorStoreTypes.set(vectorStoreType, vectorStore);
    }

    /**
     * Create or get a vector store from the provided type.
     */
    static createVectorStore(
        vectorStoreType: VectorStoreType | string,
        kwargs: Record<string, any>
    ): BaseVectorStore {
        switch (vectorStoreType) {
            case VectorStoreType.LanceDB:
                return new LanceDBVectorStore(kwargs);
            case VectorStoreType.AzureAISearch:
                return new AzureAISearchVectorStore(kwargs);
            case VectorStoreType.CosmosDB:
                return new CosmosDBVectorStore(kwargs);
            default:
                if (this.vectorStoreTypes.has(vectorStoreType)) {
                    const VectorStoreClass = this.vectorStoreTypes.get(vectorStoreType)!;
                    return new VectorStoreClass(kwargs);
                }
                const msg = `Unknown vector store type: ${vectorStoreType}`;
                throw new Error(msg);
        }
    }
}
