#!/bin/bash

# 脚本用于批量替换TypeScript/JavaScript文件中import语句里的连字符(-)为下划线(_)
# 只处理import语句，不影响其他代码

echo "开始处理import语句中的连字符替换..."

# 计数器
processed=0
changed=0

# 查找所有TypeScript和JavaScript文件
find . -type f \( -name "*.ts" -o -name "*.js" -o -name "*.tsx" -o -name "*.jsx" \) | while read -r file; do
    # 检查文件是否包含import语句和连字符
    if grep -q "import.*from.*['\"].*-.*['\"]" "$file"; then
        echo "处理文件: $file"
        
        # 使用sed替换import语句中的连字符
        # 这个sed命令只会替换import...from '...' 或 import...from "..." 中路径部分的连字符
        sed -i.bak -E "s/(import[^;]*from[[:space:]]*['\"])([^'\"]*)/\1\2/g; s/(import[^;]*from[[:space:]]*['\"][^'\"]*)-([^'\"]*['\"])/\1_\2/g" "$file"
        
        # 更精确的替换方式
        # 使用perl进行更精确的替换
        perl -i -pe 's/(import[^;]*?from\s*['\''"])([^'\''"]*?)(['\''"])/my ($pre, $path, $post) = ($1, $2, $3); $path =~ s\/-\/_\/g; $pre . $path . $post/ge' "$file"
        
        # 删除备份文件
        rm -f "$file.bak"
        
        ((changed++))
    fi
    ((processed++))
    
    # 每处理100个文件显示进度
    if ((processed % 100 == 0)); then
        echo "已处理 $processed 个文件，修改了 $changed 个文件"
    fi
done

echo "处理完成!"
echo "总共处理了文件，修改了文件"
