﻿/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Indexing-Engine to Query Read Adapters.
 * 
 * The parts of these functions that do type adaptation, renaming, collating, etc. should eventually go away.
 * Ideally this is just a straight read-through into the object model.
 */

import { GraphRagConfig } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { Community } from '../data_model/community';
import { CommunityReport } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { Covariate } from '../data_model/covariate';
import { Entity } from '../data_model/entity';
import { Relationship } from '../data_model/relationship';
import { TextUnit } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { ModelManager } from '../language_model/manager';
import { EmbeddingModel } from '../language_model/protocol/base';
import {
    readCommunities,
    readCommunityReports,
    readCovariates,
    readEntities,
    readRelationships,
    readTextUnits,
} from './input/loaders/dfs';
import { BaseVectorStore } from '../vector_stores/base';

const logger = console;

export interface DataFrame {
    [key: string]: any[];
    length: number;
}

export function readIndexerTextUnits(finalTextUnits: DataFrame): TextUnit[] {
    return readTextUnits({
        df: finalTextUnits,
        covariatesCol: null,
    });
}

export function readIndexerCovariates(finalCovariates: DataFrame): Covariate[] {
    const covariateDF = { ...finalCovariates };
    covariateDF.id = covariateDF.id.map((id: any) => String(id));

    return readCovariates({
        df: covariateDF,
        shortIdCol: "human_readable_id",
        attributesCols: [
            "object_id",
            "status",
            "start_date",
            "end_date",
            "description",
        ],
        textUnitIdsCol: null,
    });
}

export function readIndexerRelationships(finalRelationships: DataFrame): Relationship[] {
    return readRelationships({
        df: finalRelationships,
        shortIdCol: "human_readable_id",
        rankCol: "combined_degree",
        descriptionEmbeddingCol: null,
        attributesCols: null,
    });
}

export function readIndexerReports(
    finalCommunityReports: DataFrame,
    finalCommunities: DataFrame,
    communityLevel?: number,
    dynamicCommunitySelection: boolean = false,
    contentEmbeddingCol: string = "full_content_embedding",
    config?: GraphRagConfig
): CommunityReport[] {
    let reportsDF = { ...finalCommunityReports };
    let nodesDF = explodeColumn(finalCommunities, "entity_ids");

    if (communityLevel !== undefined) {
        nodesDF = filterUnderCommunityLevel(nodesDF, communityLevel);
        reportsDF = filterUnderCommunityLevel(reportsDF, communityLevel);
    }

    if (!dynamicCommunitySelection) {
        // Perform community level roll up
        const communityCol = nodesDF.community || [];
        for (let i = 0; i < communityCol.length; i++) {
            if (communityCol[i] == null) {
                communityCol[i] = -1;
            }
        }
        nodesDF.community = communityCol.map((c: any) => parseInt(String(c)));

        const groupedNodes = groupByAndAggregateMax(nodesDF, "title", "community");
        const filteredCommunityDF = getUniqueValues(groupedNodes.community);

        reportsDF = mergeDataFrames(reportsDF, { community: filteredCommunityDF }, "community", "community", "inner");
    }

    if (config && (
        !reportsDF[contentEmbeddingCol] ||
        reportsDF[contentEmbeddingCol].some((val: any) => val == null)
    )) {
        const embeddingModelSettings = config.getLanguageModelConfig("default_embedding_model");
        const embedder = new ModelManager().getOrCreateEmbeddingModel({
            name: "default_embedding",
            modelType: embeddingModelSettings.type,
            config: embeddingModelSettings,
        });
        reportsDF = embedCommunityReports(reportsDF, embedder, contentEmbeddingCol);
    }

    return readCommunityReports({
        df: reportsDF,
        idCol: "id",
        shortIdCol: "community",
        contentEmbeddingCol,
    });
}

export function readIndexerReportEmbeddings(
    communityReports: CommunityReport[],
    embeddingsStore: BaseVectorStore
): void {
    for (const report of communityReports) {
        const searchResult = embeddingsStore.searchById(report.id);
        if (searchResult) {
            report.fullContentEmbedding = searchResult.vector;
        }
    }
}

export function readIndexerEntities(
    entities: DataFrame,
    communities: DataFrame,
    communityLevel?: number
): Entity[] {
    const communityJoin = selectColumns(
        explodeColumn(communities, "entity_ids"),
        ["community", "level", "entity_ids"]
    );

    let nodesDF = mergeDataFrames(entities, communityJoin, "id", "entity_ids", "left");

    if (communityLevel !== undefined) {
        nodesDF = filterUnderCommunityLevel(nodesDF, communityLevel);
    }

    nodesDF = selectColumns(nodesDF, ["id", "community"]);

    // Fill null communities with -1
    const communityCol = nodesDF.community || [];
    for (let i = 0; i < communityCol.length; i++) {
        if (communityCol[i] == null) {
            communityCol[i] = -1;
        }
    }
    nodesDF.community = communityCol;

    // Group entities by id and collect unique community IDs
    const groupedNodes = groupByAndAggregateSet(nodesDF, "id", "community");

    // Convert sets to string arrays
    groupedNodes.community = groupedNodes.community.map((communitySet: Set<any>) =>
        Array.from(communitySet).map(i => String(parseInt(String(i))))
    );

    const finalDF = mergeDataFrames(groupedNodes, entities, "id", "id", "inner");
    const uniqueDF = removeDuplicates(finalDF, ["id"]);

    return readEntities({
        df: uniqueDF,
        idCol: "id",
        titleCol: "title",
        typeCol: "type",
        shortIdCol: "human_readable_id",
        descriptionCol: "description",
        communityCol: "community",
        rankCol: "degree",
        nameEmbeddingCol: null,
        descriptionEmbeddingCol: "description_embedding",
        textUnitIdsCol: "text_unit_ids",
    });
}

export function readIndexerCommunities(
    finalCommunities: DataFrame,
    finalCommunityReports: DataFrame
): Community[] {
    const communitiesDF = { ...finalCommunities };
    const nodesDF = explodeColumn(communitiesDF, "entity_ids");
    const reportsDF = { ...finalCommunityReports };

    // Ensure communities matches community reports
    const reportCommunities = new Set(reportsDF.community || []);
    const missingReports = (communitiesDF.community || []).filter(
        (community: any) => !reportCommunities.has(community)
    );

    if (missingReports.length > 0) {
        logger.warn("Missing reports for communities:", missingReports);
        // Filter to only include communities with reports
        const filteredCommunitiesDF = filterByValues(communitiesDF, "community", Array.from(reportCommunities));
        return readCommunities({
            df: filteredCommunitiesDF,
            idCol: "id",
            shortIdCol: "community",
            titleCol: "title",
            levelCol: "level",
            entitiesCol: null,
            relationshipsCol: null,
            covariatesCol: null,
            parentCol: "parent",
            childrenCol: "children",
            attributesCols: null,
        });
    }

    return readCommunities({
        df: communitiesDF,
        idCol: "id",
        shortIdCol: "community",
        titleCol: "title",
        levelCol: "level",
        entitiesCol: null,
        relationshipsCol: null,
        covariatesCol: null,
        parentCol: "parent",
        childrenCol: "children",
        attributesCols: null,
    });
}

export function embedCommunityReports(
    reportsDF: DataFrame,
    embedder: EmbeddingModel,
    sourceCol: string = "full_content",
    embeddingCol: string = "full_content_embedding"
): DataFrame {
    if (!reportsDF[sourceCol]) {
        throw new Error(`Reports missing ${sourceCol} column`);
    }

    if (!reportsDF[embeddingCol]) {
        const sourceData = reportsDF[sourceCol] || [];
        const embeddings = sourceData.map((content: string) => embedder.embed(content));
        reportsDF[embeddingCol] = embeddings;
    }

    return reportsDF;
}

function filterUnderCommunityLevel(df: DataFrame, communityLevel: number): DataFrame {
    const levelCol = df.level || [];
    const filteredIndices: number[] = [];

    for (let i = 0; i < df.length; i++) {
        if (levelCol[i] <= communityLevel) {
            filteredIndices.push(i);
        }
    }

    const result: DataFrame = { length: filteredIndices.length };
    for (const [key, values] of Object.entries(df)) {
        if (key !== 'length') {
            result[key] = filteredIndices.map(i => values[i]);
        }
    }

    return result;
}

// Helper functions for DataFrame operations
function explodeColumn(df: DataFrame, columnName: string): DataFrame {
    const result: DataFrame = { length: 0 };
    const explodedRows: any[] = [];

    const columnData = df[columnName] || [];
    const otherColumns = Object.keys(df).filter(key => key !== columnName && key !== 'length');

    for (let i = 0; i < df.length; i++) {
        const values = columnData[i];
        if (Array.isArray(values)) {
            for (const value of values) {
                const row: any = { [columnName]: value };
                for (const col of otherColumns) {
                    row[col] = df[col][i];
                }
                explodedRows.push(row);
            }
        } else if (values != null) {
            const row: any = { [columnName]: values };
            for (const col of otherColumns) {
                row[col] = df[col][i];
            }
            explodedRows.push(row);
        }
    }

    result.length = explodedRows.length;
    const allColumns = [...new Set([columnName, ...otherColumns])];

    for (const col of allColumns) {
        result[col] = explodedRows.map(row => row[col]);
    }

    return result;
}

function selectColumns(df: DataFrame, columns: string[]): DataFrame {
    const result: DataFrame = { length: df.length };

    for (const col of columns) {
        if (df[col] !== undefined) {
            result[col] = [...df[col]];
        } else {
            result[col] = new Array(df.length).fill(null);
        }
    }

    return result;
}

function mergeDataFrames(
    left: DataFrame,
    right: DataFrame,
    leftOn: string,
    rightOn: string,
    how: "inner" | "left" | "right" | "outer"
): DataFrame {
    const result: DataFrame = { length: 0 };
    const mergedRows: any[] = [];

    const leftKeys = left[leftOn] || [];
    const rightKeys = right[rightOn] || [];
    const leftColumns = Object.keys(left).filter(key => key !== 'length');
    const rightColumns = Object.keys(right).filter(key => key !== 'length');
    const allColumns = [...new Set([...leftColumns, ...rightColumns])];

    if (how === "inner") {
        for (let i = 0; i < left.length; i++) {
            const leftKey = leftKeys[i];
            for (let j = 0; j < right.length; j++) {
                const rightKey = rightKeys[j];
                if (leftKey === rightKey) {
                    const mergedRow: any = {};
                    for (const col of leftColumns) {
                        if (col !== 'length') {
                            mergedRow[col] = left[col][i];
                        }
                    }
                    for (const col of rightColumns) {
                        if (col !== 'length' && col !== rightOn) {
                            mergedRow[col] = right[col][j];
                        }
                    }
                    mergedRows.push(mergedRow);
                }
            }
        }
    } else if (how === "left") {
        for (let i = 0; i < left.length; i++) {
            const leftKey = leftKeys[i];
            let found = false;

            for (let j = 0; j < right.length; j++) {
                const rightKey = rightKeys[j];
                if (leftKey === rightKey) {
                    const mergedRow: any = {};
                    for (const col of leftColumns) {
                        if (col !== 'length') {
                            mergedRow[col] = left[col][i];
                        }
                    }
                    for (const col of rightColumns) {
                        if (col !== 'length') {
                            mergedRow[col] = right[col][j];
                        }
                    }
                    mergedRows.push(mergedRow);
                    found = true;
                }
            }

            if (!found) {
                const mergedRow: any = {};
                for (const col of leftColumns) {
                    if (col !== 'length') {
                        mergedRow[col] = left[col][i];
                    }
                }
                for (const col of rightColumns) {
                    if (col !== 'length') {
                        mergedRow[col] = null;
                    }
                }
                mergedRows.push(mergedRow);
            }
        }
    }

    result.length = mergedRows.length;
    for (const col of allColumns) {
        if (col !== 'length') {
            result[col] = mergedRows.map(row => row[col] || null);
        }
    }

    return result;
}

function groupByAndAggregateMax(df: DataFrame, groupByCol: string, aggregateCol: string): DataFrame {
    const groups = new Map<any, number>();
    const groupKeys = df[groupByCol] || [];
    const aggregateValues = df[aggregateCol] || [];

    for (let i = 0; i < df.length; i++) {
        const key = groupKeys[i];
        const value = aggregateValues[i];

        if (!groups.has(key) || groups.get(key)! < value) {
            groups.set(key, value);
        }
    }

    const result: DataFrame = { length: groups.size };
    const keys = Array.from(groups.keys());
    const maxValues = Array.from(groups.values());

    result[groupByCol] = keys;
    result[aggregateCol] = maxValues;

    return result;
}

function groupByAndAggregateSet(df: DataFrame, groupByCol: string, aggregateCol: string): DataFrame {
    const groups = new Map<any, Set<any>>();
    const groupKeys = df[groupByCol] || [];
    const aggregateValues = df[aggregateCol] || [];

    for (let i = 0; i < df.length; i++) {
        const key = groupKeys[i];
        const value = aggregateValues[i];

        if (!groups.has(key)) {
            groups.set(key, new Set());
        }
        groups.get(key)!.add(value);
    }

    const result: DataFrame = { length: groups.size };
    const keys = Array.from(groups.keys());
    const sets = Array.from(groups.values());

    result[groupByCol] = keys;
    result[aggregateCol] = sets;

    return result;
}

function getUniqueValues(arr: any[]): any[] {
    return [...new Set(arr)];
}

function removeDuplicates(df: DataFrame, columns: string[]): DataFrame {
    const seen = new Set<string>();
    const uniqueIndices: number[] = [];

    for (let i = 0; i < df.length; i++) {
        const key = columns.map(col => String(df[col][i])).join('|');
        if (!seen.has(key)) {
            seen.add(key);
            uniqueIndices.push(i);
        }
    }

    const result: DataFrame = { length: uniqueIndices.length };
    for (const [key, values] of Object.entries(df)) {
        if (key !== 'length') {
            result[key] = uniqueIndices.map(i => values[i]);
        }
    }

    return result;
}

function filterByValues(df: DataFrame, column: string, values: any[]): DataFrame {
    const valueSet = new Set(values);
    const columnData = df[column] || [];
    const filteredIndices: number[] = [];

    for (let i = 0; i < df.length; i++) {
        if (valueSet.has(columnData[i])) {
            filteredIndices.push(i);
        }
    }

    const result: DataFrame = { length: filteredIndices.length };
    for (const [key, vals] of Object.entries(df)) {
        if (key !== 'length') {
            result[key] = filteredIndices.map(i => vals[i]);
        }
    }

    return result;
}
