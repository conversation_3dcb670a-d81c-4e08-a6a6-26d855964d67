// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Parameterization settings for the default configuration.
 */

import * as fs from 'fs';
import * as path from 'path';
import { graphragConfigDefaults } from '../defaults.js';
import { InputFileType, StorageType, ReportingType } from '../enums.js';
import { LanguageModelConfigMissingError } from '../errors.js';
import { VectorStoreType } from '../../vector_stores/factory.js';

// Import all config interfaces
import { BasicSearchConfig, createBasicSearchConfig } from './basic_search_config.js';
import { CacheConfig, createCacheConfig } from './cache_config.js';
import { ChunkingConfig, createChunkingConfig } from './chunking_config.js';
import { ClusterGraphConfig, createClusterGraphConfig } from './cluster_graph_config.js';
import { CommunityReportsConfig, createCommunityReportsConfig } from './community_reports_config.js';
import { DRIFTSearchConfig, createDRIFTSearchConfig } from './drift_search_config.js';
import { EmbedGraphConfig, createEmbedGraphConfig } from './embed_graph_config.js';
import { ClaimExtractionConfig, createClaimExtractionConfig } from './extract_claims_config.js';
import { ExtractGraphConfig, createExtractGraphConfig } from './extract_graph_config.js';
import { ExtractGraphNLPConfig, createExtractGraphNLPConfig } from './extract_graph_nlp_config.js';
import { GlobalSearchConfig, createGlobalSearchConfig } from './global_search_config.js';
import { InputConfig, createInputConfig } from './input_config.js';
import { LanguageModelConfig } from './language_model_config.js';
import { LocalSearchConfig, createLocalSearchConfig } from './local_search_config.js';
import { PruneGraphConfig, createPruneGraphConfig } from './prune_graph_config.js';
import { ReportingConfig, createReportingConfig } from './reporting_config.js';
import { SnapshotsConfig, createSnapshotsConfig } from './snapshots_config.js';
import { StorageConfig, createStorageConfig } from './storage_config.js';
import { SummarizeDescriptionsConfig, createSummarizeDescriptionsConfig } from './summarize_descriptions_config.js';
import { TextEmbeddingConfig, createTextEmbeddingConfig } from './text_embedding_config.js';
import { UmapConfig, createUmapConfig } from './umap_config.js';
import { VectorStoreConfig, createVectorStoreConfig } from './vector_store_config.js';

// Default model IDs
export const DEFAULT_CHAT_MODEL_ID = "default_chat";
export const DEFAULT_EMBEDDING_MODEL_ID = "default_embedding";

/**
 * Base interface for the Default-Configuration parameterization settings.
 */
export interface GraphRagConfig {
  /**
   * The root directory for the configuration.
   */
  rootDir: string;

  /**
   * Available language model configurations.
   */
  models: Record<string, LanguageModelConfig>;

  /**
   * The input configuration.
   */
  input: InputConfig;

  /**
   * The chunking configuration to use.
   */
  chunks: ChunkingConfig;

  /**
   * The output configuration.
   */
  output: StorageConfig;

  /**
   * A list of output configurations used for multi-index query.
   */
  outputs?: Record<string, StorageConfig>;

  /**
   * The output configuration for the updated index.
   */
  updateIndexOutput: StorageConfig;

  /**
   * The cache configuration.
   */
  cache: CacheConfig;

  /**
   * The reporting configuration.
   */
  reporting: ReportingConfig;

  /**
   * The vector store configuration.
   */
  vectorStore: Record<string, VectorStoreConfig>;

  /**
   * List of workflows to run, in execution order.
   */
  workflows?: string[];

  /**
   * Text embedding configuration.
   */
  embedText: TextEmbeddingConfig;

  /**
   * The entity extraction configuration to use.
   */
  extractGraph: ExtractGraphConfig;

  /**
   * The description summarization configuration to use.
   */
  summarizeDescriptions: SummarizeDescriptionsConfig;

  /**
   * The NLP-based graph extraction configuration to use.
   */
  extractGraphNlp: ExtractGraphNLPConfig;

  /**
   * The graph pruning configuration to use.
   */
  pruneGraph: PruneGraphConfig;

  /**
   * The cluster graph configuration to use.
   */
  clusterGraph: ClusterGraphConfig;

  /**
   * The claim extraction configuration to use.
   */
  extractClaims: ClaimExtractionConfig;

  /**
   * The community reports configuration to use.
   */
  communityReports: CommunityReportsConfig;

  /**
   * Graph embedding configuration.
   */
  embedGraph: EmbedGraphConfig;

  /**
   * The UMAP configuration to use.
   */
  umap: UmapConfig;

  /**
   * The snapshots configuration to use.
   */
  snapshots: SnapshotsConfig;

  /**
   * The local search configuration.
   */
  localSearch: LocalSearchConfig;

  /**
   * The global search configuration.
   */
  globalSearch: GlobalSearchConfig;

  /**
   * The drift search configuration.
   */
  driftSearch: DRIFTSearchConfig;

  /**
   * The basic search configuration.
   */
  basicSearch: BasicSearchConfig;
}

/**
 * GraphRagConfig class with validation methods.
 */
export class GraphRagConfigImpl implements GraphRagConfig {
  rootDir: string;
  models: Record<string, LanguageModelConfig>;
  input: InputConfig;
  chunks: ChunkingConfig;
  output: StorageConfig;
  outputs?: Record<string, StorageConfig>;
  updateIndexOutput: StorageConfig;
  cache: CacheConfig;
  reporting: ReportingConfig;
  vectorStore: Record<string, VectorStoreConfig>;
  workflows?: string[];
  embedText: TextEmbeddingConfig;
  extractGraph: ExtractGraphConfig;
  summarizeDescriptions: SummarizeDescriptionsConfig;
  extractGraphNlp: ExtractGraphNLPConfig;
  pruneGraph: PruneGraphConfig;
  clusterGraph: ClusterGraphConfig;
  extractClaims: ClaimExtractionConfig;
  communityReports: CommunityReportsConfig;
  embedGraph: EmbedGraphConfig;
  umap: UmapConfig;
  snapshots: SnapshotsConfig;
  localSearch: LocalSearchConfig;
  globalSearch: GlobalSearchConfig;
  driftSearch: DRIFTSearchConfig;
  basicSearch: BasicSearchConfig;

  constructor(config: Partial<GraphRagConfig> = {}) {
    // Initialize with defaults and provided config
    this.rootDir = config.rootDir ?? graphragConfigDefaults.root_dir;
    this.models = config.models ?? graphragConfigDefaults.models;
    this.input = config.input ?? createInputConfig();
    this.chunks = config.chunks ?? createChunkingConfig();
    this.output = config.output ?? createStorageConfig();
    this.outputs = config.outputs;
    this.updateIndexOutput = config.updateIndexOutput ?? createStorageConfig({
      baseDir: graphragConfigDefaults.update_index_output.base_dir
    });
    this.cache = config.cache ?? createCacheConfig();
    this.reporting = config.reporting ?? createReportingConfig();
    this.vectorStore = config.vectorStore ?? {
      "default": createVectorStoreConfig(graphragConfigDefaults.vector_store.default)
    };
    this.workflows = config.workflows;
    this.embedText = config.embedText ?? createTextEmbeddingConfig();
    this.extractGraph = config.extractGraph ?? createExtractGraphConfig();
    this.summarizeDescriptions = config.summarizeDescriptions ?? createSummarizeDescriptionsConfig();
    this.extractGraphNlp = config.extractGraphNlp ?? createExtractGraphNLPConfig();
    this.pruneGraph = config.pruneGraph ?? createPruneGraphConfig();
    this.clusterGraph = config.clusterGraph ?? createClusterGraphConfig();
    this.extractClaims = config.extractClaims ?? createClaimExtractionConfig({
      enabled: graphragConfigDefaults.extract_claims.enabled
    });
    this.communityReports = config.communityReports ?? createCommunityReportsConfig();
    this.embedGraph = config.embedGraph ?? createEmbedGraphConfig();
    this.umap = config.umap ?? createUmapConfig();
    this.snapshots = config.snapshots ?? createSnapshotsConfig();
    this.localSearch = config.localSearch ?? createLocalSearchConfig();
    this.globalSearch = config.globalSearch ?? createGlobalSearchConfig();
    this.driftSearch = config.driftSearch ?? createDRIFTSearchConfig();
    this.basicSearch = config.basicSearch ?? createBasicSearchConfig();

    // Validate the configuration
    this.validate();
  }

  /**
   * Get a string representation.
   */
  toString(): string {
    return JSON.stringify(this, null, 4);
  }

  /**
   * Validate the configuration.
   */
  private validate(): void {
    this.validateRootDir();
    this.validateModels();
    this.validateInputPattern();
    this.validateInputBaseDir();
    this.validateReportingBaseDir();
    this.validateOutputBaseDir();
    this.validateMultiOutputBaseDirs();
    this.validateUpdateIndexOutputBaseDir();
    this.validateVectorStoreDbUri();
  }

  /**
   * Validate the root directory.
   */
  private validateRootDir(): void {
    if (this.rootDir.trim() === "") {
      this.rootDir = process.cwd();
    }

    const rootDir = path.resolve(this.rootDir);
    if (!fs.existsSync(rootDir) || !fs.statSync(rootDir).isDirectory()) {
      const msg = `Invalid root directory: ${this.rootDir} is not a directory.`;
      throw new Error(msg);
    }
    this.rootDir = rootDir;
  }

  /**
   * Validate the models configuration.
   *
   * Ensure both a default chat model and default embedding model
   * have been defined. Other models may also be defined but
   * defaults are required for the time being as places of the
   * code fallback to default model configs instead
   * of specifying a specific model.
   */
  private validateModels(): void {
    if (!(DEFAULT_CHAT_MODEL_ID in this.models)) {
      throw new LanguageModelConfigMissingError(DEFAULT_CHAT_MODEL_ID);
    }
    if (!(DEFAULT_EMBEDDING_MODEL_ID in this.models)) {
      throw new LanguageModelConfigMissingError(DEFAULT_EMBEDDING_MODEL_ID);
    }
  }

  /**
   * Validate the input file pattern based on the specified type.
   */
  private validateInputPattern(): void {
    if (this.input.filePattern.length === 0) {
      if (this.input.fileType === InputFileType.TEXT) {
        this.input.filePattern = ".*\\.txt$";
      } else {
        this.input.filePattern = `.*\\.${this.input.fileType}$`;
      }
    }
  }

  /**
   * Validate the input base directory.
   */
  private validateInputBaseDir(): void {
    if (this.input.storage.type === StorageType.FILE) {
      if (this.input.storage.baseDir.trim() === "") {
        const msg = "input storage base directory is required for file input storage. Please rerun `graphrag init` and set the input storage configuration.";
        throw new Error(msg);
      }
      this.input.storage.baseDir = path.resolve(this.rootDir, this.input.storage.baseDir);
    }
  }

  /**
   * Validate the output base directory.
   */
  private validateOutputBaseDir(): void {
    if (this.output.type === StorageType.FILE) {
      if (this.output.baseDir.trim() === "") {
        const msg = "output base directory is required for file output. Please rerun `graphrag init` and set the output configuration.";
        throw new Error(msg);
      }
      this.output.baseDir = path.resolve(this.rootDir, this.output.baseDir);
    }
  }

  /**
   * Validate the outputs dict base directories.
   */
  private validateMultiOutputBaseDirs(): void {
    if (this.outputs) {
      for (const output of Object.values(this.outputs)) {
        if (output.type === StorageType.FILE) {
          if (output.baseDir.trim() === "") {
            const msg = "Output base directory is required for file output. Please rerun `graphrag init` and set the output configuration.";
            throw new Error(msg);
          }
          output.baseDir = path.resolve(this.rootDir, output.baseDir);
        }
      }
    }
  }

  /**
   * Validate the update index output base directory.
   */
  private validateUpdateIndexOutputBaseDir(): void {
    if (this.updateIndexOutput.type === StorageType.FILE) {
      if (this.updateIndexOutput.baseDir.trim() === "") {
        const msg = "update_index_output base directory is required for file output. Please rerun `graphrag init` and set the update_index_output configuration.";
        throw new Error(msg);
      }
      this.updateIndexOutput.baseDir = path.resolve(this.rootDir, this.updateIndexOutput.baseDir);
    }
  }

  /**
   * Validate the reporting base directory.
   */
  private validateReportingBaseDir(): void {
    if (this.reporting.type === ReportingType.FILE) {
      if (this.reporting.baseDir.trim() === "") {
        const msg = "Reporting base directory is required for file reporting. Please rerun `graphrag init` and set the reporting configuration.";
        throw new Error(msg);
      }
      this.reporting.baseDir = path.resolve(this.rootDir, this.reporting.baseDir);
    }
  }

  /**
   * Validate the vector store configuration.
   */
  private validateVectorStoreDbUri(): void {
    for (const store of Object.values(this.vectorStore)) {
      if (store.type === VectorStoreType.LanceDB) {
        if (!store.dbUri || store.dbUri.trim() === "") {
          const msg = "Vector store URI is required for LanceDB. Please rerun `graphrag init` and set the vector store configuration.";
          throw new Error(msg);
        }
        store.dbUri = path.resolve(this.rootDir, store.dbUri);
      }
    }
  }

  /**
   * Get a model configuration by ID.
   *
   * @param modelId - The ID of the model to get. Should match an ID in the models list.
   * @returns The model configuration if found.
   * @throws Error if the model ID is not found in the configuration.
   */
  getLanguageModelConfig(modelId: string): LanguageModelConfig {
    if (!(modelId in this.models)) {
      const errMsg = `Model ID ${modelId} not found in configuration. Please rerun \`graphrag init\` and set the model configuration.`;
      throw new Error(errMsg);
    }
    return this.models[modelId];
  }

  /**
   * Get a vector store configuration by ID.
   *
   * @param vectorStoreId - The ID of the vector store to get. Should match an ID in the vector_store list.
   * @returns The vector store configuration if found.
   * @throws Error if the vector store ID is not found in the configuration.
   */
  getVectorStoreConfig(vectorStoreId: string): VectorStoreConfig {
    if (!(vectorStoreId in this.vectorStore)) {
      const errMsg = `Vector Store ID ${vectorStoreId} not found in configuration. Please rerun \`graphrag init\` and set the vector store configuration.`;
      throw new Error(errMsg);
    }
    return this.vectorStore[vectorStoreId];
  }
}

/**
 * Create a GraphRagConfig with default values.
 */
export function createGraphRagConfig(config: Partial<GraphRagConfig> = {}): GraphRagConfig {
  return new GraphRagConfigImpl(config);
}
