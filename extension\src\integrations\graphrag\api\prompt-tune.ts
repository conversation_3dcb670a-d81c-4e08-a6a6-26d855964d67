// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Auto Templating API.
 * 
 * This API provides access to the auto templating feature of graphrag, allowing external applications
 * to hook into graphrag and generate prompts from private data.
 * 
 * WARNING: This API is under development and may undergo changes in future releases.
 * Backwards compatibility is not guaranteed at this time.
 */

import { NoopWorkflowCallbacks } from '../callbacks/noop-workflow-callbacks';
import { graphragConfigDefaults } from '../config/defaults';
import { GraphRagConfig } from '../config/models/graph-rag-config';
import { ModelManager } from '../language_model/manager';
import { initLoggers } from '../logger/standard-logging';
import { DocSelectionType } from '../prompt_tune/types';

// Constants from prompt_tune defaults
const MAX_TOKEN_COUNT = 8000;
const PROMPT_TUNING_MODEL_ID = "default_chat_model";

// Mock implementations for prompt generation functions
// In a real implementation, these would be imported from actual modules

async function loadDocsInChunks(options: {
    config: GraphRagConfig;
    limit: number;
    selectMethod: DocSelectionType;
    chunkSize: number;
    overlap: number;
    nSubsetMax: number;
    k: number;
}): Promise<string[]> {
    // Mock implementation
    return ["Sample document 1", "Sample document 2"];
}

async function generateDomain(llm: any, docList: string[]): Promise<string> {
    // Mock implementation
    return "General Knowledge";
}

async function detectLanguage(llm: any, docList: string[]): Promise<string> {
    // Mock implementation
    return "English";
}

async function generatePersona(llm: any, domain: string): Promise<string> {
    // Mock implementation
    return "You are a helpful AI assistant";
}

async function generateCommunityReportRating(llm: any, options: {
    domain: string;
    persona: string;
    docs: string[];
}): Promise<string> {
    // Mock implementation
    return "Rate community reports based on relevance and accuracy";
}

async function generateEntityTypes(llm: any, options: {
    domain: string;
    persona: string;
    docs: string[];
    jsonMode: boolean;
}): Promise<string[] | null> {
    // Mock implementation
    return ["PERSON", "ORGANIZATION", "LOCATION"];
}

async function generateEntityRelationshipExamples(llm: any, options: {
    persona: string;
    entityTypes?: string[] | null;
    docs: string[];
    language: string;
    jsonMode: boolean;
}): Promise<string> {
    // Mock implementation
    return "Example entity relationships";
}

function createExtractGraphPrompt(options: {
    entityTypes?: string[] | null;
    docs: string[];
    examples: string;
    language: string;
    jsonMode: boolean;
    encodingModel: string;
    maxTokenCount: number;
    minExamplesRequired: number;
}): string {
    // Mock implementation
    return "Extract entities and relationships from the following text...";
}

function createEntitySummarizationPrompt(options: {
    persona: string;
    language: string;
}): string {
    // Mock implementation
    return "Summarize the following entity information...";
}

async function generateCommunityReporterRole(llm: any, options: {
    domain: string;
    persona: string;
    docs: string[];
}): Promise<string> {
    // Mock implementation
    return "Community reporter role description";
}

function createCommunitySummarizationPrompt(options: {
    persona: string;
    role: string;
    reportRatingDescription: string;
    language: string;
}): string {
    // Mock implementation
    return "Create a comprehensive community report...";
}

/**
 * Generate indexing prompts.
 * 
 * @param config - The GraphRag configuration.
 * @param chunkSize - The chunk token size to use for input text units.
 * @param overlap - The overlap between chunks.
 * @param limit - The limit of chunks to load.
 * @param selectionMethod - The chunk selection method.
 * @param domain - The domain to map the input documents to.
 * @param language - The language to use for the prompts.
 * @param maxTokens - The maximum number of tokens to use on entity extraction prompts.
 * @param discoverEntityTypes - Generate entity types.
 * @param minExamplesRequired - The minimum number of examples required for entity extraction prompts.
 * @param nSubsetMax - The number of text chunks to embed when using auto selection method.
 * @param k - The number of documents to select when using auto selection method.
 * @returns A tuple containing: entity extraction prompt, entity summarization prompt, community summarization prompt.
 */
export async function generateIndexingPrompts(options: {
    config: GraphRagConfig;
    chunkSize?: number;
    overlap?: number;
    limit?: number;
    selectionMethod?: DocSelectionType;
    domain?: string | null;
    language?: string | null;
    maxTokens?: number;
    discoverEntityTypes?: boolean;
    minExamplesRequired?: number;
    nSubsetMax?: number;
    k?: number;
}): Promise<[string, string, string]> {
    const {
        config,
        chunkSize = graphragConfigDefaults.chunks.size,
        overlap = graphragConfigDefaults.chunks.overlap,
        limit = 15,
        selectionMethod = DocSelectionType.RANDOM,
        domain = null,
        language = null,
        maxTokens = MAX_TOKEN_COUNT,
        discoverEntityTypes = true,
        minExamplesRequired = 2,
        nSubsetMax = 300,
        k = 15
    } = options;

    initLoggers({ config });

    // Retrieve documents
    console.log("Chunking documents...");
    const docList = await loadDocsInChunks({
        config,
        limit,
        selectMethod: selectionMethod,
        chunkSize,
        overlap,
        nSubsetMax,
        k,
    });

    // Create LLM from config
    // TODO: Expose a way to specify Prompt Tuning model ID through config
    console.log("Retrieving language model configuration...");
    const defaultLlmSettings = config.getLanguageModelConfig(PROMPT_TUNING_MODEL_ID);

    console.log("Creating language model...");
    const llm = ModelManager.getInstance().registerChat(
        "prompt_tuning",
        defaultLlmSettings.type as string,
        {
            config: defaultLlmSettings,
            callbacks: new NoopWorkflowCallbacks(),
            cache: null,
        }
    );

    let actualDomain = domain;
    if (!actualDomain) {
        console.log("Generating domain...");
        actualDomain = await generateDomain(llm, docList);
    }

    let actualLanguage = language;
    if (!actualLanguage) {
        console.log("Detecting language...");
        actualLanguage = await detectLanguage(llm, docList);
    }

    console.log("Generating persona...");
    const persona = await generatePersona(llm, actualDomain);

    console.log("Generating community report ranking description...");
    const communityReportRanking = await generateCommunityReportRating(llm, {
        domain: actualDomain,
        persona,
        docs: docList
    });

    let entityTypes: string[] | null = null;
    const extractGraphLlmSettings = config.getLanguageModelConfig(
        config.extract_graph.model_id
    );
    
    if (discoverEntityTypes) {
        console.log("Generating entity types...");
        entityTypes = await generateEntityTypes(llm, {
            domain: actualDomain,
            persona,
            docs: docList,
            jsonMode: extractGraphLlmSettings.model_supports_json || false,
        });
    }

    console.log("Generating entity relationship examples...");
    const examples = await generateEntityRelationshipExamples(llm, {
        persona,
        entityTypes,
        docs: docList,
        language: actualLanguage,
        jsonMode: false, // config.llm.model_supports_json should be used, but these prompts are used in non-json mode by the index engine
    });

    console.log("Generating entity extraction prompt...");
    const extractGraphPrompt = createExtractGraphPrompt({
        entityTypes,
        docs: docList,
        examples,
        language: actualLanguage,
        jsonMode: false, // config.llm.model_supports_json should be used, but these prompts are used in non-json mode by the index engine
        encodingModel: extractGraphLlmSettings.encoding_model,
        maxTokenCount: maxTokens,
        minExamplesRequired,
    });

    console.log("Generating entity summarization prompt...");
    const entitySummarizationPrompt = createEntitySummarizationPrompt({
        persona,
        language: actualLanguage,
    });

    console.log("Generating community reporter role...");
    const communityReporterRole = await generateCommunityReporterRole(llm, {
        domain: actualDomain,
        persona,
        docs: docList
    });

    console.log("Generating community summarization prompt...");
    const communitySummarizationPrompt = createCommunitySummarizationPrompt({
        persona,
        role: communityReporterRole,
        reportRatingDescription: communityReportRanking,
        language: actualLanguage,
    });

    console.debug(`Generated domain: ${actualDomain}`);
    console.debug(`Detected language: ${actualLanguage}`);
    console.debug(`Generated persona: ${persona}`);

    return [
        extractGraphPrompt,
        entitySummarizationPrompt,
        communitySummarizationPrompt,
    ];
}