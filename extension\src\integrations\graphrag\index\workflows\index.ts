﻿/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A package containing all built-in workflow definitions.
 */

import { PipelineFactory } from './factory';

// Import all workflow functions
import { runWorkflow as runCreateBaseTextUnits }
import { runWorkflow as runCreateCommunities }
import { runWorkflow as runCreateCommunityReports }
import { runWorkflow as runCreateCommunityReportsText }
import { runWorkflow as runCreateFinalDocuments }
import { runWorkflow as runCreateFinalTextUnits }
import { runWorkflow as runExtractCovariates }
import { runWorkflow as runExtractGraph }
import { runWorkflow as runExtractGraphNlp }
import { runWorkflow as runFinalizeGraph }
import { runWorkflow as runGenerateTextEmbeddings }
import { runWorkflow as runLoadInputDocuments }
import { runWorkflow as runLoadUpdateDocuments }
import { runWorkflow as runPruneGraph }
import { runWorkflow as runUpdateCleanState }
import { runWorkflow as runUpdateCommunities }
import { runWorkflow as runUpdateCommunityReports }
import { runWorkflow as runUpdateCovariates }
import { runWorkflow as runUpdateEntitiesRelationships }
import { runWorkflow as runUpdateFinalDocuments }
import { runWorkflow as runUpdateTextEmbeddings }
import { runWorkflow as runUpdateTextUnits }

// Register all built-in workflows at once
PipelineFactory.registerAll({
    "load_input_documents": runLoadInputDocuments,
    "load_update_documents": runLoadUpdateDocuments,
    "create_base_text_units": runCreateBaseTextUnits,
    "create_communities": runCreateCommunities,
    "create_community_reports_text": runCreateCommunityReportsText,
    "create_community_reports": runCreateCommunityReports,
    "extract_covariates": runExtractCovariates,
    "create_final_documents": runCreateFinalDocuments,
    "create_final_text_units": runCreateFinalTextUnits,
    "extract_graph_nlp": runExtractGraphNlp,
    "extract_graph": runExtractGraph,
    "finalize_graph": runFinalizeGraph,
    "generate_text_embeddings": runGenerateTextEmbeddings,
    "prune_graph": runPruneGraph,
    "update_final_documents": runUpdateFinalDocuments,
    "update_text_embeddings": runUpdateTextEmbeddings,
    "update_community_reports": runUpdateCommunityReports,
    "update_entities_relationships": runUpdateEntitiesRelationships,
    "update_communities": runUpdateCommunities,
    "update_covariates": runUpdateCovariates,
    "update_text_units": runUpdateTextUnits,
    "update_clean_state": runUpdateCleanState,
});

// Export factory and individual workflows
export { PipelineFactory };
export * from './factory';
export * from './load-input-documents';
export * from './create-base-text-units';
export * from './extract-graph';
export * from './create-communities';
export * from './generate-text-embeddings';
