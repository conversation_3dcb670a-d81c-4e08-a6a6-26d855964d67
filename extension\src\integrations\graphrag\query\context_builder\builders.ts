﻿/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Base classes for global and local context builders.
 */

import { ConversationHistory } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;

export interface DataFrame {
    [key: string]: any[];
    length: number;
}

export interface ContextBuilderResult {
    contextChunks: string | string[];
    contextRecords: Record<string, DataFrame>;
    llmCalls?: number;
    promptTokens?: number;
    outputTokens?: number;
}

export abstract class GlobalContextBuilder {
    abstract buildContext(
        query: string,
        conversationHistory?: ConversationHistory,
        ...kwargs: any[]
    ): Promise<ContextBuilderResult>;
}

export abstract class LocalContextBuilder {
    abstract buildContext(
        query: string,
        conversationHistory?: ConversationHistory,
        ...kwargs: any[]
    ): ContextBuilderResult;
}

export abstract class DRIFTContextBuilder {
    abstract buildContext(
        query: string,
        ...kwargs: any[]
    ): Promise<[DataFrame, Record<string, number>]>;
}

export abstract class BasicContextBuilder {
    abstract buildContext(
        query: string,
        conversationHistory?: ConversationHistory,
        ...kwargs: any[]
    ): ContextBuilderResult;
}
