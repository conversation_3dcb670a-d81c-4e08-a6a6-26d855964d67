/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing cluster_graph, apply_clustering and run_layout methods definition.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import * as nx from 'networkx';
import { stable_largest_connected_component } from '../utils/stable_lcc.js';

const logger = console;

// Python: Communities = list[tuple[int, int, int, list[str]]]
export type Communities = Array<[number, number, number, string[]]>;

/**
 * Apply a hierarchical clustering algorithm to a graph.
 * Matches the Python cluster_graph function exactly.
 */
export function cluster_graph(
    graph: nx.Graph,
    max_cluster_size: number,
    use_lcc: boolean,
    seed?: number | null
): Communities {
    // Python: if len(graph.nodes) == 0:
    if (graph.nodes().length === 0) {
        // Python: logger.warning("Graph has no nodes")
        logger.warn("Graph has no nodes");
        // Python: return []
        return [];
    }

    // Python: node_id_to_community_map, parent_mapping = _compute_leiden_communities(...)
    const [node_id_to_community_map, parent_mapping] = _compute_leiden_communities(
        graph,
        max_cluster_size,
        use_lcc,
        seed
    );

    // Python: levels = sorted(node_id_to_community_map.keys())
    const levels = Object.keys(node_id_to_community_map).map(Number).sort((a, b) => a - b);

    // Python: clusters: dict[int, dict[int, list[str]]] = {}
    const clusters: Record<number, Record<number, string[]>> = {};

    // Python: for level in levels:
    for (const level of levels) {
        // Python: result = {}
        const result: Record<number, string[]> = {};
        // Python: clusters[level] = result
        clusters[level] = result;

        // Python: for node_id, raw_community_id in node_id_to_community_map[level].items():
        for (const [node_id, raw_community_id] of Object.entries(node_id_to_community_map[level])) {
            // Python: community_id = raw_community_id
            const community_id = raw_community_id;
            // Python: if community_id not in result:
            if (!(community_id in result)) {
                // Python: result[community_id] = []
                result[community_id] = [];
            }
            // Python: result[community_id].append(node_id)
            result[community_id].push(node_id);
        }
    }

    // Python: results: Communities = []
    const results: Communities = [];
    // Python: for level in clusters:
    for (const level of levels) {
        // Python: for cluster_id, nodes in clusters[level].items():
        for (const [cluster_id_str, nodes] of Object.entries(clusters[level])) {
            const cluster_id = parseInt(cluster_id_str, 10);
            // Python: results.append((level, cluster_id, parent_mapping[cluster_id], nodes))
            results.push([level, cluster_id, parent_mapping[cluster_id], nodes]);
        }
    }

    // Python: return results
    return results;
}

// Python: # Taken from graph_intelligence & adapted
// Python: def _compute_leiden_communities(...)
/**
 * Return Leiden root communities and their hierarchy mapping.
 * Matches the Python _compute_leiden_communities function exactly.
 */
function _compute_leiden_communities(
    graph: nx.Graph | nx.DiGraph,
    max_cluster_size: number,
    use_lcc: boolean,
    seed?: number | null
): [Record<number, Record<string, number>>, Record<number, number>] {
    // Python: if use_lcc:
    //     graph = stable_largest_connected_component(graph)
    let working_graph = graph;
    if (use_lcc) {
        working_graph = stable_largest_connected_component(graph);
    }

    // Python: community_mapping = hierarchical_leiden(
    //     graph, max_cluster_size=max_cluster_size, random_seed=seed
    // )
    // Note: This is a simplified implementation. In production, you would use hierarchical_leiden
    const community_mapping = hierarchical_leiden_simplified(working_graph, max_cluster_size, seed);

    // Python: results: dict[int, dict[str, int]] = {}
    const results: Record<number, Record<string, number>> = {};
    // Python: hierarchy: dict[int, int] = {}
    const hierarchy: Record<number, number> = {};

    // Python: for partition in community_mapping:
    for (const partition of community_mapping) {
        // Python: results[partition.level] = results.get(partition.level, {})
        if (!(partition.level in results)) {
            results[partition.level] = {};
        }
        // Python: results[partition.level][partition.node] = partition.cluster
        results[partition.level][partition.node] = partition.cluster;

        // Python: hierarchy[partition.cluster] = (
        //     partition.parent_cluster if partition.parent_cluster is not None else -1
        // )
        hierarchy[partition.cluster] = partition.parent_cluster !== null ? partition.parent_cluster : -1;
    }

    // Python: return results, hierarchy
    return [results, hierarchy];
}

/**
 * Simplified hierarchical Leiden clustering implementation.
 * In production, you would use the graspologic.partition.hierarchical_leiden function.
 */
interface Partition {
    level: number;
    node: string;
    cluster: number;
    parent_cluster: number | null;
}

function hierarchical_leiden_simplified(
    graph: nx.Graph | nx.DiGraph,
    max_cluster_size: number,
    seed?: number | null
): Partition[] {
    const partitions: Partition[] = [];
    const nodes = graph.nodes();

    // Simple single-level clustering for demonstration
    let cluster_id = 0;
    const visited = new Set<string>();

    for (const node of nodes) {
        if (!visited.has(node)) {
            const cluster_nodes = [node];
            visited.add(node);

            // Find connected nodes up to max_cluster_size
            const neighbors = graph.neighbors(node);
            for (const neighbor of neighbors) {
                if (!visited.has(neighbor) && cluster_nodes.length < max_cluster_size) {
                    cluster_nodes.push(neighbor);
                    visited.add(neighbor);
                }
            }

            // Create partitions for all nodes in this cluster
            for (const cluster_node of cluster_nodes) {
                partitions.push({
                    level: 0,
                    node: cluster_node,
                    cluster: cluster_id,
                    parent_cluster: null
                });
            }

            cluster_id++;
        }
    }

    return partitions;
}

// Compatibility export for existing code
export const clusterGraph = cluster_graph;