/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing the 'PipelineRunContext' models.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { PipelineCache } from '../../cache/pipeline_cache.js';
import { WorkflowCallbacks } from '../../callbacks/workflow_callbacks.js';
import { PipelineStorage } from '../../storage/pipeline_storage.js';
import { PipelineState } from './state.js';
import { PipelineRunStats } from './stats.js';

/**
 * Provides the context for the current pipeline run.
 * Matches the Python PipelineRunContext dataclass exactly.
 */
export interface PipelineRunContext {
    /** Pipeline run statistics */
    stats: PipelineRunStats;

    /** Storage for input documents */
    input_storage: PipelineStorage;

    /** Long-term storage for pipeline verbs to use. Items written here will be written to the storage provider */
    output_storage: PipelineStorage;

    /** Storage for previous pipeline run when running in update mode */
    previous_storage: PipelineStorage;

    /** Cache instance for reading previous LLM responses */
    cache: PipelineCache;

    /** Callbacks to be called during the pipeline run */
    callbacks: WorkflowCallbacks;

    /** Arbitrary property bag for runtime state, persistent pre-computes, or experimental features */
    state: PipelineState;

    // Compatibility properties for existing code (camelCase versions)
    /** @deprecated Use input_storage instead */
    inputStorage: PipelineStorage;
    /** @deprecated Use output_storage instead */
    outputStorage: PipelineStorage;
    /** @deprecated Use previous_storage instead */
    previousStorage: PipelineStorage;
}