/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing create_graph definition.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import * as nx from 'networkx';
import { DataFrame } from '../../data_model/types.js';

/**
 * Create a networkx graph from nodes and edges dataframes.
 * Matches the Python create_graph function exactly.
 */
export function create_graph(
    edges: DataFrame,
    edge_attr?: (string | number)[] | null,
    nodes?: DataFrame | null,
    node_id: string = "title"
): nx.Graph {
    // Python: graph = nx.from_pandas_edgelist(edges, edge_attr=edge_attr)
    const graph = nx.from_pandas_edgelist(edges, { edge_attr });

    // Python: if nodes is not None:
    if (nodes !== null && nodes !== undefined) {
        // Python: nodes.set_index(node_id, inplace=True)
        // Python: graph.add_nodes_from((n, dict(d)) for n, d in nodes.iterrows())

        // Convert DataFrame to node data
        for (const row of nodes.data) {
            const node_key = row[node_id];
            const node_data = { ...row };
            delete node_data[node_id]; // Remove the ID field from data

            // Add node with data
            graph.add_node(node_key, node_data);
        }
    }

    // Python: return graph
    return graph;
}

// Compatibility export for existing code
export const createGraph = create_graph;