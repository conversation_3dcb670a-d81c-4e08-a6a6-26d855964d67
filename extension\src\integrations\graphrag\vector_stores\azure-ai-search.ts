// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * A package containing the Azure AI Search vector store implementation.
 */

import { TextEmbedder } from '../data_model/types';
import {
    DEFAULT_VECTOR_SIZE,
    BaseVectorStore,
    VectorStoreDocument,
    VectorStoreSearchResult,
} from './base';

// Azure SDK interfaces (would need to be imported from actual Azure SDK)
interface AzureKeyCredential {
    key: string;
}

interface DefaultAzureCredential {
    // Azure credential interface
}

interface SearchClient {
    uploadDocuments(documents: any[]): Promise<any>;
    search(options: any): Promise<any[]>;
    getDocument(id: string): Promise<any>;
}

interface SearchIndexClient {
    listIndexNames(): string[];
    deleteIndex(name: string): Promise<void>;
    createOrUpdateIndex(index: any): Promise<void>;
}

interface VectorizedQuery {
    vector: number[];
    k_nearest_neighbors: number;
    fields: string;
}

// Mock implementations for Azure SDK components
class MockAzureKeyCredential implements AzureKeyCredential {
    constructor(public key: string) {}
}

class MockDefaultAzureCredential implements DefaultAzureCredential {}

class MockSearchClient implements SearchClient {
    constructor(private options: any) {}

    async uploadDocuments(documents: any[]): Promise<any> {
        // Mock implementation
        return Promise.resolve();
    }

    async search(options: any): Promise<any[]> {
        // Mock implementation
        return Promise.resolve([]);
    }

    async getDocument(id: string): Promise<any> {
        // Mock implementation
        return Promise.resolve({});
    }
}

class MockSearchIndexClient implements SearchIndexClient {
    constructor(private options: any) {}

    listIndexNames(): string[] {
        // Mock implementation
        return [];
    }

    async deleteIndex(name: string): Promise<void> {
        // Mock implementation
        return Promise.resolve();
    }

    async createOrUpdateIndex(index: any): Promise<void> {
        // Mock implementation
        return Promise.resolve();
    }
}

/**
 * Azure AI Search vector storage implementation.
 */
export class AzureAISearchVectorStore extends BaseVectorStore {
    private indexClient!: SearchIndexClient;
    private vectorSize: number = DEFAULT_VECTOR_SIZE;
    private vectorSearchProfileName: string = "vectorSearchProfile";

    constructor(kwargs: Record<string, any> = {}) {
        super(
            kwargs.collection_name || '',
            kwargs.db_connection,
            kwargs.document_collection,
            kwargs.query_filter,
            kwargs
        );
    }

    /**
     * Connect to AI search vector storage.
     */
    async connect(kwargs: Record<string, any> = {}): Promise<void> {
        const url = kwargs.url;
        const apiKey = kwargs.api_key;
        const audience = kwargs.audience;
        this.vectorSize = kwargs.vector_size || DEFAULT_VECTOR_SIZE;

        this.vectorSearchProfileName = kwargs.vector_search_profile_name || "vectorSearchProfile";

        if (url) {
            const audienceArg = audience && !apiKey ? { audience } : {};
            
            // In a real implementation, these would be imported from @azure/search-documents
            this.db_connection = new MockSearchClient({
                endpoint: url,
                index_name: this.collection_name,
                credential: apiKey ? new MockAzureKeyCredential(apiKey) : new MockDefaultAzureCredential(),
                ...audienceArg,
            });

            this.indexClient = new MockSearchIndexClient({
                endpoint: url,
                credential: apiKey ? new MockAzureKeyCredential(apiKey) : new MockDefaultAzureCredential(),
                ...audienceArg,
            });
        } else {
            const notSupportedError = "Azure AI Search expects `url`.";
            throw new Error(notSupportedError);
        }
    }

    /**
     * Load documents into an Azure AI Search index.
     */
    async loadDocuments(documents: VectorStoreDocument[], overwrite: boolean = true): Promise<void> {
        if (overwrite) {
            if (this.indexClient.listIndexNames().includes(this.collection_name)) {
                await this.indexClient.deleteIndex(this.collection_name);
            }

            // Configure vector search profile
            const vectorSearch = {
                algorithms: [
                    {
                        name: "HnswAlg",
                        kind: "hnsw",
                        parameters: {
                            metric: "cosine"
                        },
                    }
                ],
                profiles: [
                    {
                        name: this.vectorSearchProfileName,
                        algorithm_configuration_name: "HnswAlg",
                    }
                ],
            };

            // Configure the index
            const index = {
                name: this.collection_name,
                fields: [
                    {
                        name: "id",
                        type: "Edm.String",
                        key: true,
                    },
                    {
                        name: "vector",
                        type: "Collection(Edm.Single)",
                        searchable: true,
                        hidden: false, // DRIFT needs to return the vector for client-side similarity
                        vector_search_dimensions: this.vectorSize,
                        vector_search_profile_name: this.vectorSearchProfileName,
                    },
                    {
                        name: "text",
                        type: "Edm.String",
                        searchable: true,
                    },
                    {
                        name: "attributes",
                        type: "Edm.String",
                    },
                ],
                vector_search: vectorSearch,
            };

            await this.indexClient.createOrUpdateIndex(index);
        }

        const batch = documents
            .filter(doc => doc.vector !== null && doc.vector !== undefined)
            .map(doc => ({
                id: doc.id,
                vector: doc.vector,
                text: doc.text,
                attributes: JSON.stringify(doc.attributes),
            }));

        if (batch.length > 0) {
            await (this.db_connection as SearchClient).uploadDocuments(batch);
        }
    }

    /**
     * Build a query filter to filter documents by a list of ids.
     */
    filterById(includeIds: (string | number)[]): any {
        if (!includeIds || includeIds.length === 0) {
            this.query_filter = null;
            return this.query_filter;
        }

        // More info about odata filtering here: https://learn.microsoft.com/en-us/azure/search/search-query-odata-search-in-function
        // search.in is faster that joined and/or conditions
        const idFilter = includeIds.map(id => String(id)).join(',');
        this.query_filter = `search.in(id, '${idFilter}', ',')`;

        return this.query_filter;
    }

    /**
     * Perform a vector-based similarity search.
     */
    async similaritySearchByVector(
        queryEmbedding: number[],
        k: number = 10,
        kwargs: Record<string, any> = {}
    ): Promise<VectorStoreSearchResult[]> {
        const vectorizedQuery: VectorizedQuery = {
            vector: queryEmbedding,
            k_nearest_neighbors: k,
            fields: "vector"
        };

        const response = await (this.db_connection as SearchClient).search({
            vector_queries: [vectorizedQuery],
        });

        return response.map(doc => ({
            document: {
                id: doc.id || "",
                text: doc.text || "",
                vector: doc.vector || [],
                attributes: JSON.parse(doc.attributes || "{}"),
            },
            // Cosine similarity between 0.333 and 1.000
            // https://learn.microsoft.com/en-us/azure/search/hybrid-search-ranking#scores-in-a-hybrid-search-results
            score: doc["@search.score"] || 0,
        }));
    }

    /**
     * Perform a text-based similarity search.
     */
    async similaritySearchByText(
        text: string,
        textEmbedder: TextEmbedder,
        k: number = 10,
        kwargs: Record<string, any> = {}
    ): Promise<VectorStoreSearchResult[]> {
        const queryEmbedding = textEmbedder(text);
        if (queryEmbedding) {
            return this.similaritySearchByVector(queryEmbedding, k);
        }
        return [];
    }

    /**
     * Search for a document by id.
     */
    async searchById(id: string): Promise<VectorStoreDocument> {
        const response = await (this.db_connection as SearchClient).getDocument(id);
        return {
            id: response.id || "",
            text: response.text || "",
            vector: response.vector || [],
            attributes: JSON.parse(response.attributes || "{}"),
        };
    }
}