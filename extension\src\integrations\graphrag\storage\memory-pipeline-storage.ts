/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 * 
 * A module containing 'MemoryPipelineStorage' model.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { FilePipelineStorage } from './file-pipeline-storage'
import { PipelineStorage } from './pipeline-storage'

/**
 * In memory storage class definition.
 */
export class MemoryPipelineStorage extends FilePipelineStorage {
    private _storage: Map<string, any> = new Map()

    constructor() {
        super()
        this._storage = new Map()
    }

    /**
     * Get the value for the given key.
     * 
     * @param key - The key to get the value for.
     * @param asBytes - Whether or not to return the value as bytes.
     * @param encoding - The encoding to use.
     * @returns The value for the given key.
     */
    async get(
        key: string, 
        asBytes?: boolean, 
        encoding?: string
    ): Promise<any> {
        return this._storage.get(key)
    }

    /**
     * Set the value for the given key.
     * 
     * @param key - The key to set the value for.
     * @param value - The value to set.
     * @param encoding - The encoding to use.
     */
    async set(
        key: string, 
        value: any, 
        encoding?: string
    ): Promise<void> {
        this._storage.set(key, value)
    }

    /**
     * Return True if the given key exists in the storage.
     * 
     * @param key - The key to check for.
     * @returns True if the key exists in the storage, False otherwise.
     */
    async has(key: string): Promise<boolean> {
        return this._storage.has(key)
    }

    /**
     * Delete the given key from the storage.
     * 
     * @param key - The key to delete.
     */
    async delete(key: string): Promise<void> {
        this._storage.delete(key)
    }

    /**
     * Clear the storage.
     */
    async clear(): Promise<void> {
        this._storage.clear()
    }

    /**
     * Create a child storage instance.
     */
    child(name?: string): PipelineStorage {
        return new MemoryPipelineStorage()
    }

    /**
     * Return the keys in the storage.
     */
    keys(): string[] {
        return Array.from(this._storage.keys())
    }
}