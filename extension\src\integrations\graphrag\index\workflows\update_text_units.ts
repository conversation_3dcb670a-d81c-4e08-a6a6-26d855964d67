﻿/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing runWorkflow method definition.
 */

import { GraphRagConfig } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { getUpdateStorages } from '../run/utils';
import { PipelineRunContext } from '../typing/context';
import { WorkflowFunctionOutput } from '../typing/workflow';
import { PipelineStorage } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { loadTableFromStorage, writeTableToStorage } from '../../utils/storage';

const logger = console;

export interface DataFrame {
    [key: string]: any[];
    length: number;
}

/**
 * Update the text units from a incremental index run.
 */
export async function runWorkflow(
    config: GraphRagConfig,
    context: PipelineRunContext,
): Promise<WorkflowFunctionOutput> {
    logger.info("Workflow started: update_text_units");
    
    const { outputStorage, previousStorage, deltaStorage } = getUpdateStorages(
        config, 
        context.state["update_timestamp"]
    );
    const entityIdMapping = context.state["incremental_update_entity_id_mapping"];

    const mergedTextUnits = await updateTextUnits(
        previousStorage, 
        deltaStorage, 
        outputStorage, 
        entityIdMapping
    );

    context.state["incremental_update_merged_text_units"] = mergedTextUnits;

    logger.info("Workflow completed: update_text_units");
    return { result: null };
}

/**
 * Update the text units output.
 */
async function updateTextUnits(
    previousStorage: PipelineStorage,
    deltaStorage: PipelineStorage,
    outputStorage: PipelineStorage,
    entityIdMapping: Record<string, any>,
): Promise<DataFrame> {
    const oldTextUnits = await loadTableFromStorage("text_units", previousStorage);
    const deltaTextUnits = await loadTableFromStorage("text_units", deltaStorage);
    const mergedTextUnits = updateAndMergeTextUnits(
        oldTextUnits, 
        deltaTextUnits, 
        entityIdMapping
    );

    await writeTableToStorage(mergedTextUnits, "text_units", outputStorage);

    return mergedTextUnits;
}

/**
 * Update and merge text units.
 * 
 * @param oldTextUnits - The old text units.
 * @param deltaTextUnits - The delta text units.
 * @param entityIdMapping - The entity id mapping.
 * @returns The updated text units.
 */
function updateAndMergeTextUnits(
    oldTextUnits: DataFrame,
    deltaTextUnits: DataFrame,
    entityIdMapping: Record<string, any>,
): DataFrame {
    // Create a copy of delta text units to avoid modifying the original
    const updatedDeltaTextUnits = { ...deltaTextUnits };

    // Look for entity ids in entity_ids and replace them with the corresponding id in the mapping
    if (entityIdMapping && Object.keys(entityIdMapping).length > 0) {
        const entityIds = deltaTextUnits.entity_ids || [];
        const updatedEntityIds = entityIds.map((entityIdList: any) => {
            if (Array.isArray(entityIdList)) {
                return entityIdList.map((id: any) => entityIdMapping[id] || id);
            }
            return entityIdList;
        });
        updatedDeltaTextUnits.entity_ids = updatedEntityIds;
    }

    // Get the max human readable id from the old text units and update the delta text units
    const oldHumanReadableIds = oldTextUnits.human_readable_id || [];
    const maxId = Math.max(...oldHumanReadableIds, 0);
    const initialId = maxId + 1;
    
    // Create new human_readable_id array for delta text units
    const newHumanReadableIds = Array.from(
        { length: deltaTextUnits.length }, 
        (_, i) => initialId + i
    );
    updatedDeltaTextUnits.human_readable_id = newHumanReadableIds;

    // Merge the final text units
    return concatenateDataFrames([oldTextUnits, updatedDeltaTextUnits]);
}

/**
 * Concatenate multiple DataFrames.
 */
function concatenateDataFrames(dataframes: DataFrame[]): DataFrame {
    if (dataframes.length === 0) {
        return { length: 0 };
    }

    const result: DataFrame = { length: 0 };
    
    // Get all unique column names
    const allColumns = new Set<string>();
    for (const df of dataframes) {
        Object.keys(df).forEach(key => {
            if (key !== 'length') {
                allColumns.add(key);
            }
        });
    }

    // Calculate total length
    const totalLength = dataframes.reduce((sum, df) => sum + df.length, 0);
    result.length = totalLength;

    // Concatenate each column
    for (const column of allColumns) {
        const concatenatedColumn: any[] = [];
        
        for (const df of dataframes) {
            const columnData = df[column] || new Array(df.length).fill(null);
            concatenatedColumn.push(...columnData);
        }
        
        result[column] = concatenatedColumn;
    }

    return result;
}
