﻿/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing runWorkflow method definition.
 */

import { GraphRagConfig }
import { getUpdateStorages } from '../run/utils';
import { PipelineRunContext } from '../typing/context';
import { WorkflowFunctionOutput } from '../typing/workflow';
import { updateAndMergeCommunityReports } from '../update/communities';
import { PipelineStorage }
import { loadTableFromStorage, writeTableToStorage } from '../../utils/storage';

const logger = console;

export interface DataFrame {
    [key: string]: any[];
    length: number;
}

/**
 * Update the community reports from a incremental index run.
 */
export async function runWorkflow(
    config: GraphRagConfig,
    context: PipelineRunContext,
): Promise<WorkflowFunctionOutput> {
    logger.info("Workflow started: update_community_reports");
    
    const { outputStorage, previousStorage, deltaStorage } = getUpdateStorages(
        config, 
        context.state["update_timestamp"]
    );

    const communityIdMapping = context.state["incremental_update_community_id_mapping"];

    const mergedCommunityReports = await updateCommunityReports(
        previousStorage, 
        deltaStorage, 
        outputStorage, 
        communityIdMapping
    );

    context.state["incremental_update_merged_community_reports"] = mergedCommunityReports;

    logger.info("Workflow completed: update_community_reports");
    return { result: null };
}

/**
 * Update the community reports output.
 */
async function updateCommunityReports(
    previousStorage: PipelineStorage,
    deltaStorage: PipelineStorage,
    outputStorage: PipelineStorage,
    communityIdMapping: Record<string, any>,
): Promise<DataFrame> {
    const oldCommunityReports = await loadTableFromStorage("community_reports", previousStorage);
    const deltaCommunityReports = await loadTableFromStorage("community_reports", deltaStorage);
    
    const mergedCommunityReports = updateAndMergeCommunityReports(
        oldCommunityReports, 
        deltaCommunityReports, 
        communityIdMapping
    );

    await writeTableToStorage(mergedCommunityReports, "community_reports", outputStorage);

    return mergedCommunityReports;
}
