﻿// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Default method for loading config.
 */

import * as fs from 'fs';
import * as path from 'path';
import * as yaml 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { config as dotenvConfig } from 'dotenv';
import { createGraphragConfig } from './create_graphrag_config.js';
import { GraphRagConfig } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;

const DEFAULT_CONFIG_FILES = ["settings.yaml", "settings.yml", "settings.json"];

/**
 * Resolve the config path from the given root directory.
 *
 * @param root - The path to the root directory containing the config file.
 *               Searches for a default config file (settings.{yaml,yml,json}).
 * @returns returns a Path if there is a config in the root directory
 *          Otherwise returns null.
 */
function searchForConfigInRootDir(root: string): string | null {
    const rootPath = path.resolve(root);

    if (!fs.existsSync(rootPath) || !fs.statSync(rootPath).isDirectory()) {
        throw new Error(`Invalid config path: ${rootPath} is not a directory`);
    }

    for (const file of DEFAULT_CONFIG_FILES) {
        const filePath = path.join(rootPath, file);
        if (fs.existsSync(filePath) && fs.statSync(filePath).isFile()) {
            return filePath;
        }
    }

    return null;
}

/**
 * Parse environment variables in the configuration text.
 *
 * @param text - The configuration text.
 * @returns The configuration text with environment variables parsed.
 * @throws Error if an environment variable is not found.
 */
function parseEnvVariables(text: string): string {
    return text.replace(/\$\{([^}]+)\}/g, (match, varName) => {
        const value = process.env[varName];
        if (value === undefined) {
            throw new Error(`Environment variable ${varName} not found`);
        }
        return value;
    });
}

/**
 * Load the .env file if it exists in the same directory as the config file.
 *
 * @param configPath - The path to the config file.
 */
function loadDotenv(configPath: string): void {
    const configDir = path.dirname(configPath);
    const dotenvPath = path.join(configDir, '.env');
    if (fs.existsSync(dotenvPath)) {
        dotenvConfig({ path: dotenvPath });
    }
}

/**
 * Get the configuration file path.
 *
 * @param rootDir - The root directory of the project. Will search for the config file in this directory.
 * @param configFilepath - The path to the config file.
 *                        If null, searches for config file in root.
 * @returns The configuration file path.
 */
function getConfigPath(rootDir: string, configFilepath?: string): string {
    if (configFilepath) {
        const configPath = path.resolve(configFilepath);
        if (!fs.existsSync(configPath)) {
            throw new Error(`Specified Config file not found: ${configPath}`);
        }
        return configPath;
    } else {
        const configPath = searchForConfigInRootDir(rootDir);
        if (!configPath) {
            throw new Error(`Config file not found in root directory: ${rootDir}`);
        }
        return configPath;
    }
}

/**
 * Apply the overrides to the raw configuration.
 */
function applyOverrides(data: Record<string, any>, overrides: Record<string, any>): void {
    for (const [key, value] of Object.entries(overrides)) {
        const keys = key.split('.');
        let target = data;
        let currentPath = keys[0];

        for (let i = 0; i < keys.length - 1; i++) {
            const k = keys[i];
            if (i > 0) {
                currentPath += `.${k}`;
            }

            const targetObj = target[k] || {};
            if (typeof targetObj !== 'object' || targetObj === null || Array.isArray(targetObj)) {
                throw new TypeError(`Cannot override non-dict value: data[${currentPath}] is not a dict.`);
            }
            target[k] = targetObj;
            target = target[k];
        }
        target[keys[keys.length - 1]] = value;
    }
}

/**
 * Parse configuration based on file extension.
 */
function parseConfig(fileExtension: string, contents: string): Record<string, any> {
    switch (fileExtension) {
        case '.yaml':
        case '.yml':
            return yaml.load(contents) as Record<string, any>;
        case '.json':
            return JSON.parse(contents);
        default:
            throw new Error(`Unable to parse config. Unsupported file extension: ${fileExtension}`);
    }
}

/**
 * Load configuration from a file.
 *
 * @param rootDir - The root directory of the project. Will search for the config file in this directory.
 * @param configFilepath - The path to the config file.
 *                        If null, searches for config file in root.
 * @param cliOverrides - A flat dictionary of cli overrides.
 *                      Example: {'output.base_dir': 'override_value'}
 * @returns The loaded configuration.
 * @throws Error if the config file is not found, extension is not supported,
 *         applying cli overrides fails, config file references a non-existent environment variable,
 *         or there are validation errors when instantiating the config.
 */
export function loadConfig(
    rootDir: string,
    configFilepath?: string,
    cliOverrides?: Record<string, any>
): GraphRagConfig {
    const root = path.resolve(rootDir);
    const configPath = getConfigPath(root, configFilepath);
    loadDotenv(configPath);

    const configExtension = path.extname(configPath);
    const configText = fs.readFileSync(configPath, 'utf-8');
    const parsedConfigText = parseEnvVariables(configText);
    const configData = parseConfig(configExtension, parsedConfigText);

    if (cliOverrides) {
        applyOverrides(configData, cliOverrides);
    }

    return createGraphragConfig(configData, root);
}
