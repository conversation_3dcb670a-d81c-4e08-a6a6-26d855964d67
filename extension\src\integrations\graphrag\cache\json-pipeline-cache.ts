// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * A module containing 'JsonPipelineCache' model.
 */

import { PipelineCache } from './pipeline-cache';
import { PipelineStorage } from '../storage/pipeline-storage';

/**
 * File pipeline cache class definition.
 */
export class JsonPipelineCache extends PipelineCache {
    private storage: PipelineStorage;
    private encoding: string;

    constructor(storage: PipelineStorage, encoding: string = "utf-8") {
        super();
        this.storage = storage;
        this.encoding = encoding;
    }

    /**
     * Get method definition.
     */
    async get(key: string): Promise<string | null> {
        if (await this.has(key)) {
            try {
                const data = await this.storage.get(key, { encoding: this.encoding });
                const parsedData = JSON.parse(data);
                return parsedData.result;
            } catch (error) {
                if (error instanceof SyntaxError || error instanceof Error) {
                    // Handle UnicodeDecodeError equivalent and JSONDecodeError
                    await this.storage.delete(key);
                    return null;
                }
                throw error;
            }
        }
        return null;
    }

    /**
     * Set method definition.
     */
    async set(key: string, value: any, debugData?: Record<string, any> | null): Promise<void> {
        if (value === null || value === undefined) {
            return;
        }
        const data = { result: value, ...(debugData || {}) };
        await this.storage.set(
            key,
            JSON.stringify(data, null, 0),
            { encoding: this.encoding }
        );
    }

    /**
     * Has method definition.
     */
    async has(key: string): Promise<boolean> {
        return await this.storage.has(key);
    }

    /**
     * Delete method definition.
     */
    async delete(key: string): Promise<void> {
        if (await this.has(key)) {
            await this.storage.delete(key);
        }
    }

    /**
     * Clear method definition.
     */
    async clear(): Promise<void> {
        await this.storage.clear();
    }

    /**
     * Child method definition.
     */
    child(name: string): JsonPipelineCache {
        return new JsonPipelineCache(this.storage.child(name), this.encoding);
    }
}