﻿// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * DRIFT Search implementation.
 */

import { Encoding } from 'tiktoken';
import { QueryCallbacks } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { ChatModel } from '../../../language_model/protocol/base';
import { getOpenaiModelParametersFromDict } from '../../../language_model/providers/fnllm/utils';
import { ConversationHistory } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { EntityVectorStoreKey } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { numTokens } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { BaseSearch, SearchResult } from '../base';
import { DriftAction } from './action';
import { DRIFTSearchContextBuilder } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { DRIFTPrimer } from './primer';
import { QueryState } from './state';
import { LocalSearch } from '../local_search/search';

const logger = console;

export interface DRIFTSearchConfig {
    model: ChatModel;
    contextBuilder: DRIFTSearchContextBuilder;
    tokenEncoder?: Encoding;
    queryState?: QueryState;
    callbacks?: QueryCallbacks[];
}

export class DRIFTSearch extends BaseSearch<DRIFTSearchContextBuilder> {
    private readonly contextBuilder: DRIFTSearchContextBuilder;
    private readonly tokenEncoder?: Encoding;
    private readonly queryState: QueryState;
    private readonly primer: DRIFTPrimer;
    private readonly callbacks: QueryCallbacks[];
    private readonly localSearch: LocalSearch;

    constructor(config: DRIFTSearchConfig) {
        super(config.model, config.contextBuilder, config.tokenEncoder);

        this.contextBuilder = config.contextBuilder;
        this.tokenEncoder = config.tokenEncoder;
        this.queryState = config.queryState || new QueryState();
        this.primer = new DRIFTPrimer({
            config: this.contextBuilder.config,
            chatModel: config.model,
            tokenEncoder: config.tokenEncoder,
        });
        this.callbacks = config.callbacks || [];
        this.localSearch = this.initLocalSearch();
    }

    /**
     * Initialize the LocalSearch object with parameters based on the DRIFT search configuration.
     */
    private initLocalSearch(): LocalSearch {
        const localContextParams = {
            textUnitProp: this.contextBuilder.config.localSearchTextUnitProp,
            communityProp: this.contextBuilder.config.localSearchCommunityProp,
            topKMappedEntities: this.contextBuilder.config.localSearchTopKMappedEntities,
            topKRelationships: this.contextBuilder.config.localSearchTopKRelationships,
            includeEntityRank: true,
            includeRelationshipWeight: true,
            includeCommunityRank: false,
            returnCandidateContext: false,
            embeddingVectorstoreKey: EntityVectorStoreKey.ID,
            maxContextTokens: this.contextBuilder.config.localSearchMaxDataTokens,
        };

        const modelParams = getOpenaiModelParametersFromDict({
            model: this.model.config.model,
            max_tokens: this.contextBuilder.config.localSearchLlmMaxGenTokens,
            temperature: this.contextBuilder.config.localSearchTemperature,
            n: this.contextBuilder.config.localSearchN,
            top_p: this.contextBuilder.config.localSearchTopP,
            max_completion_tokens: this.contextBuilder.config.localSearchLlmMaxGenCompletionTokens,
            response_format: { type: "json_object" },
        });

        return new LocalSearch({
            model: this.model,
            systemPrompt: this.contextBuilder.localSystemPrompt,
            contextBuilder: this.contextBuilder.localMixedContext,
            tokenEncoder: this.tokenEncoder,
            modelParams,
            contextBuilderParams: localContextParams,
            responseType: "multiple paragraphs",
            callbacks: this.callbacks,
        });
    }

    /**
     * Process the results from the primer search to extract intermediate answers and follow-up queries.
     */
    private processResultsFromPrimer(query: string, searchResults: SearchResult): DriftAction {
        const response = searchResults.response;
        
        if (Array.isArray(response) && response.length > 0 && typeof response[0] === 'object') {
            const intermediateAnswers = response
                .filter(item => item.intermediate_answer)
                .map(item => item.intermediate_answer);

            if (intermediateAnswers.length === 0) {
                const errorMsg = "No intermediate answers found in primer response. Ensure that the primer response includes intermediate answers.";
                throw new Error(errorMsg);
            }

            const intermediateAnswer = intermediateAnswers.join('\n\n');

            const followUps = response.flatMap(item => item.follow_up_queries || []);

            if (followUps.length === 0) {
                const errorMsg = "No follow-up queries found in primer response. Ensure that the primer response includes follow-up queries.";
                throw new Error(errorMsg);
            }

            const score = response.reduce((sum, item) => sum + (item.score || Number.NEGATIVE_INFINITY), 0) / response.length;
            
            const responseData = {
                intermediate_answer: intermediateAnswer,
                follow_up_queries: followUps,
                score: score,
            };
            
            return DriftAction.fromPrimerResponse(query, responseData);
        }
        
        const errorMsg = "Response must be a list of dictionaries.";
        throw new Error(errorMsg);
    }

    /**
     * Perform an asynchronous search step by executing each DriftAction asynchronously.
     */
    private async searchStep(
        globalQuery: string, 
        searchEngine: LocalSearch, 
        actions: DriftAction[]
    ): Promise<DriftAction[]> {
        const tasks = actions.map(action => 
            action.search(searchEngine, globalQuery)
        );
        return await Promise.all(tasks);
    }

    /**
     * Perform an asynchronous DRIFT search.
     */
    public async search(
        query: string,
        conversationHistory?: any,
        reduce: boolean = true,
        kwargs?: Record<string, any>
    ): Promise<SearchResult> {
        if (query === "") {
            const errorMsg = "DRIFT Search query cannot be empty.";
            throw new Error(errorMsg);
        }

        const llmCalls: Record<string, number> = {};
        const promptTokens: Record<string, number> = {};
        const outputTokens: Record<string, number> = {};

        const startTime = performance.now();

        // Check if query state is empty
        if (!this.queryState.hasGraph()) {
            // Prime the search with the primer
            const [primerContext, tokenCt] = await this.contextBuilder.buildContext(query);
            llmCalls.build_context = tokenCt.llm_calls;
            promptTokens.build_context = tokenCt.prompt_tokens;
            outputTokens.build_context = tokenCt.output_tokens;

            const primerResponse = await this.primer.search(query, primerContext);
            llmCalls.primer = primerResponse.llmCalls;
            promptTokens.primer = primerResponse.promptTokens;
            outputTokens.primer = primerResponse.outputTokens;

            // Package response into DriftAction
            const initAction = this.processResultsFromPrimer(query, primerResponse);
            this.queryState.addAction(initAction);
            this.queryState.addAllFollowUps(initAction, initAction.followUps);
        }

        // Main loop
        let epochs = 0;
        const maxDepth = this.contextBuilder.config.nDepth || 3;
        
        while (epochs < maxDepth) {
            const actions = this.queryState.rankIncompleteActions();
            if (actions.length === 0) {
                logger.debug("No more actions to take. Exiting DRIFT loop.");
                break;
            }
            
            const limitedActions = actions.slice(0, this.contextBuilder.config.driftKFollowups || 5);
            
            // Process actions
            const results = await this.searchStep(query, this.localSearch, limitedActions);

            // Update query state
            for (const action of results) {
                this.queryState.addAction(action);
                this.queryState.addAllFollowUps(action, action.followUps);
            }
            epochs++;
        }

        const tElapsed = (performance.now() - startTime) / 1000; // Convert to seconds

        // Calculate token usage
        const tokenCt = this.queryState.actionTokenCount();
        llmCalls.action = tokenCt.llm_calls;
        promptTokens.action = tokenCt.prompt_tokens;
        outputTokens.action = tokenCt.output_tokens;

        // Package up context data
        const [responseState, contextData, contextText] = this.queryState.serialize(true);

        let reducedResponse = responseState;
        if (reduce) {
            // Reduce response_state to a single comprehensive response
            for (const callback of this.callbacks) {
                callback.onReduceResponseStart?.(responseState);
            }

            const modelParams = getOpenaiModelParametersFromDict({
                model: this.model.config.model,
                max_tokens: this.contextBuilder.config.reduceMaxTokens,
                temperature: this.contextBuilder.config.reduceTemperature,
                max_completion_tokens: this.contextBuilder.config.reduceMaxCompletionTokens,
            });

            reducedResponse = await this.reduceResponse(
                responseState,
                query,
                llmCalls,
                promptTokens,
                outputTokens,
                { modelParams }
            );

            for (const callback of this.callbacks) {
                callback.onReduceResponseEnd?.(reducedResponse);
            }
        }

        return new SearchResult({
            response: reducedResponse,
            contextData,
            contextText,
            completionTime: tElapsed,
            llmCalls: Object.values(llmCalls).reduce((sum, val) => sum + val, 0),
            promptTokens: Object.values(promptTokens).reduce((sum, val) => sum + val, 0),
            outputTokens: Object.values(outputTokens).reduce((sum, val) => sum + val, 0),
            llmCallsCategories: llmCalls,
            promptTokensCategories: promptTokens,
            outputTokensCategories: outputTokens,
        });
    }

    /**
     * Perform a streaming DRIFT search asynchronously.
     */
    public async *streamSearch(
        query: string,
        conversationHistory?: ConversationHistory
    ): AsyncGenerator<string, void, unknown> {
        const result = await this.search(query, conversationHistory, false);

        let response = result.response;
        if (Array.isArray(response)) {
            response = response[0];
        }

        for (const callback of this.callbacks) {
            callback.onReduceResponseStart?.(response);
        }

        const modelParams = getOpenaiModelParametersFromDict({
            model: this.model.config.model,
            max_tokens: this.contextBuilder.config.reduceMaxTokens,
            temperature: this.contextBuilder.config.reduceTemperature,
            max_completion_tokens: this.contextBuilder.config.reduceMaxCompletionTokens,
        });

        let fullResponse = "";
        for await (const resp of this.reduceResponseStreaming(response, query, modelParams)) {
            fullResponse += resp;
            yield resp;
        }

        for (const callback of this.callbacks) {
            callback.onReduceResponseEnd?.(fullResponse);
        }
    }

    /**
     * Reduce the response to a single comprehensive response.
     */
    private async reduceResponse(
        responses: string | Record<string, any>,
        query: string,
        llmCalls: Record<string, number>,
        promptTokens: Record<string, number>,
        outputTokens: Record<string, number>,
        llmKwargs: Record<string, any>
    ): Promise<string> {
        let reduceResponses: string[] = [];

        if (typeof responses === 'string') {
            reduceResponses = [responses];
        } else {
            reduceResponses = (responses.nodes || [])
                .filter((response: any) => response.answer)
                .map((response: any) => response.answer);
        }

        const searchPrompt = this.contextBuilder.reduceSystemPrompt
            .replace('{context_data}', JSON.stringify(reduceResponses))
            .replace('{response_type}', this.contextBuilder.responseType || 'comprehensive answer');

        const searchMessages = [
            { role: "system", content: searchPrompt },
        ];

        const modelResponse = await this.model.achat(
            query,
            searchMessages,
            llmKwargs.modelParams || {}
        );

        const reducedResponse = modelResponse.output.content;

        llmCalls.reduce = 1;
        promptTokens.reduce = numTokens(searchPrompt, this.tokenEncoder) + numTokens(query, this.tokenEncoder);
        outputTokens.reduce = numTokens(reducedResponse, this.tokenEncoder);

        return reducedResponse;
    }

    /**
     * Reduce the response to a single comprehensive response with streaming.
     */
    private async *reduceResponseStreaming(
        responses: string | Record<string, any>,
        query: string,
        modelParams: Record<string, any>
    ): AsyncGenerator<string, void, unknown> {
        let reduceResponses: string[] = [];

        if (typeof responses === 'string') {
            reduceResponses = [responses];
        } else {
            reduceResponses = (responses.nodes || [])
                .filter((response: any) => response.answer)
                .map((response: any) => response.answer);
        }

        const searchPrompt = this.contextBuilder.reduceSystemPrompt
            .replace('{context_data}', JSON.stringify(reduceResponses))
            .replace('{response_type}', this.contextBuilder.responseType || 'comprehensive answer');

        const searchMessages = [
            { role: "system", content: searchPrompt },
        ];

        for await (const response of this.model.achatStream(
            query,
            searchMessages,
            modelParams
        )) {
            for (const callback of this.callbacks) {
                callback.onLlmNewToken?.(response);
            }
            yield response;
        }
    }
}
