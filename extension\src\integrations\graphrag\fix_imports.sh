#!/bin/bash

echo "开始批量替换import语句中的连字符为下划线..."

# 计数器
total_files=0
changed_files=0

# 查找所有TypeScript和JavaScript文件
find . -type f \( -name "*.ts" -o -name "*.js" -o -name "*.tsx" -o -name "*.jsx" \) -print0 | while IFS= read -r -d '' file; do
    ((total_files++))
    
    # 检查文件是否包含需要替换的import语句
    if grep -q "import.*from.*['\"].*-.*['\"]" "$file"; then
        echo "处理文件: $file"
        
        # 创建临时文件
        temp_file=$(mktemp)
        
        # 逐行处理文件
        while IFS= read -r line; do
            # 如果是import语句且包含连字符
            if [[ $line =~ import.*from.*[\'\"]*.*-.*[\'\"]*  ]]; then
                # 只在import语句的路径部分替换连字符
                # 使用sed替换from后面引号内的连字符
                new_line=$(echo "$line" | sed -E "s/(import[^;]*from[[:space:]]*['\"])([^'\"]*)/\1\2/g")
                new_line=$(echo "$new_line" | sed -E "s/(from[[:space:]]*['\"][^'\"]*)-([^'\"]*['\"])/\1_\2/g")
                
                # 继续替换直到没有更多连字符
                while [[ $new_line =~ from[[:space:]]*[\'\"]*[^\'\"]*-[^\'\"]*[\'\"]*  ]]; do
                    new_line=$(echo "$new_line" | sed -E "s/(from[[:space:]]*['\"][^'\"]*)-([^'\"]*['\"])/\1_\2/g")
                done
                
                echo "$new_line" >> "$temp_file"
                
                if [[ "$line" != "$new_line" ]]; then
                    echo "  替换: $line"
                    echo "  ->    $new_line"
                fi
            else
                echo "$line" >> "$temp_file"
            fi
        done < "$file"
        
        # 替换原文件
        mv "$temp_file" "$file"
        ((changed_files++))
    fi
    
    # 每处理50个文件显示进度
    if ((total_files % 50 == 0)); then
        echo "已处理 $total_files 个文件，修改了 $changed_files 个文件"
    fi
done

echo "处理完成!"
echo "总共处理了 $total_files 个文件，修改了 $changed_files 个文件"
