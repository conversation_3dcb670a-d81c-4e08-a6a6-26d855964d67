﻿// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Parameterization settings for the extract graph configuration.
 */

import { readFileSync } from 'fs';
import { join } from 'path';
import { graphragConfigDefaults } from '../defaults.js';
import { LanguageModelConfig } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { ExtractEntityStrategyType } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;

/**
 * Configuration section for entity extraction.
 */
export interface ExtractGraphConfig {
  /**
   * The model ID to use for text embeddings.
   */
  modelId: string;

  /**
   * The entity extraction prompt to use.
   */
  prompt?: string;

  /**
   * The entity extraction entity types to use.
   */
  entityTypes: string[];

  /**
   * The maximum number of entity gleanings to use.
   */
  maxGleanings: number;

  /**
   * Override the default entity extraction strategy.
   */
  strategy?: Record<string, any>;
}

/**
 * Create an ExtractGraphConfig with default values.
 */
export function createExtractGraphConfig(config: Partial<ExtractGraphConfig> = {}): ExtractGraphConfig {
  return {
    modelId: config.modelId ?? graphragConfigDefaults.extractGraph.modelId,
    prompt: config.prompt ?? graphragConfigDefaults.extractGraph.prompt,
    entityTypes: config.entityTypes ?? graphragConfigDefaults.extractGraph.entityTypes,
    maxGleanings: config.maxGleanings ?? graphragConfigDefaults.extractGraph.maxGleanings,
    strategy: config.strategy ?? graphragConfigDefaults.extractGraph.strategy,
  };
}

/**
 * Get the resolved entity extraction strategy.
 */
export function getResolvedExtractGraphStrategy(
  config: ExtractGraphConfig,
  rootDir: string,
  modelConfig: LanguageModelConfig
): Record<string, any> {
  return config.strategy || {
    type: ExtractEntityStrategyType.GraphIntelligence,
    llm: modelConfig,
    extractionPrompt: config.prompt
      ? readFileSync(join(rootDir, config.prompt), 'utf-8')
      : undefined,
    maxGleanings: config.maxGleanings,
  };
}
