﻿/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing DataFrame utilities.
 */

import { DataFrame }

/**
 * Drop columns from a dataframe.
 * @param df - The DataFrame to modify
 * @param columns - Column names to drop
 * @returns New DataFrame without the specified columns
 */
export function dropColumns(df: DataFrame, ...columns: string[]): DataFrame {
    const newColumns = df.columns.filter(col => !columns.includes(col));
    const newData = df.data.map(row => {
        const newRow: Record<string, any> = {};
        newColumns.forEach(col => {
            if (col in row) {
                newRow[col] = row[col];
            }
        });
        return newRow;
    });
    
    return {
        columns: newColumns,
        data: newData
    };
}

/**
 * Return a filtered DataFrame where a column equals a value.
 * @param df - The DataFrame to filter
 * @param column - Column name to filter on
 * @param value - Value to match
 * @returns Filtered DataFrame
 */
export function whereColumnEquals(df: DataFrame, column: string, value: any): DataFrame {
    const filteredData = df.data.filter(row => row[column] === value);
    return {
        columns: df.columns,
        data: filteredData
    };
}

/**
 * Return an anti-joined dataframe.
 * @param df - The DataFrame to apply the exclusion to
 * @param exclude - The DataFrame containing rows to remove
 * @param column - The join-on column
 * @returns DataFrame with excluded rows removed
 */
export function antijoin(df: DataFrame, exclude: DataFrame, column: string): DataFrame {
    const excludeValues = new Set(exclude.data.map(row => row[column]));
    const filteredData = df.data.filter(row => !excludeValues.has(row[column]));
    
    return {
        columns: df.columns,
        data: filteredData
    };
}

/**
 * Apply a transformation function to a series (column).
 * @param df - The DataFrame
 * @param column - Column name to transform
 * @param fn - Transformation function
 * @returns New DataFrame with transformed column
 */
export function transformSeries(df: DataFrame, column: string, fn: (value: any) => any): DataFrame {
    const newData = df.data.map(row => ({
        ...row,
        [column]: fn(row[column])
    }));
    
    return {
        columns: df.columns,
        data: newData
    };
}

/**
 * Perform a table join.
 * @param left - Left DataFrame
 * @param right - Right DataFrame
 * @param key - Join key column
 * @param strategy - Join strategy ('left', 'right', 'inner', 'outer')
 * @returns Joined DataFrame
 */
export function join(
    left: DataFrame,
    right: DataFrame,
    key: string,
    strategy: 'left' | 'right' | 'inner' | 'outer' = 'left'
): DataFrame {
    // Create lookup map for right DataFrame
    const rightMap = new Map<any, Record<string, any>>();
    right.data.forEach(row => {
        rightMap.set(row[key], row);
    });
    
    const allColumns = new Set([...left.columns, ...right.columns]);
    const joinedData: Record<string, any>[] = [];
    
    switch (strategy) {
        case 'left':
            left.data.forEach(leftRow => {
                const rightRow = rightMap.get(leftRow[key]) || {};
                joinedData.push({ ...leftRow, ...rightRow });
            });
            break;
        case 'inner':
            left.data.forEach(leftRow => {
                const rightRow = rightMap.get(leftRow[key]);
                if (rightRow) {
                    joinedData.push({ ...leftRow, ...rightRow });
                }
            });
            break;
        // Add other join strategies as needed
        default:
            throw new Error(`Join strategy ${strategy} not implemented`);
    }
    
    return {
        columns: Array.from(allColumns),
        data: joinedData
    };
}

/**
 * Perform a union operation on the given set of dataframes.
 * @param frames - DataFrames to union
 * @returns Combined DataFrame
 */
export function union(...frames: DataFrame[]): DataFrame {
    const allColumns = new Set<string>();
    const allData: Record<string, any>[] = [];
    
    frames.forEach(df => {
        df.columns.forEach(col => allColumns.add(col));
        allData.push(...df.data);
    });
    
    return {
        columns: Array.from(allColumns),
        data: allData
    };
}

/**
 * Select columns from a dataframe.
 * @param df - The DataFrame
 * @param columns - Column names to select
 * @returns DataFrame with only selected columns
 */
export function select(df: DataFrame, ...columns: string[]): DataFrame {
    const newData = df.data.map(row => {
        const newRow: Record<string, any> = {};
        columns.forEach(col => {
            if (col in row) {
                newRow[col] = row[col];
            }
        });
        return newRow;
    });
    
    return {
        columns: columns,
        data: newData
    };
}
