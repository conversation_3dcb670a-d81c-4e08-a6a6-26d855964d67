﻿/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Encapsulates pipeline construction and selection.
 */

import { IndexingMethod } from '../../config/enums';
import { GraphRagConfig }
import { Pipeline } from '../typing/pipeline';
import { WorkflowFunction } from '../typing/workflow';

const logger = console;

/**
 * A factory class for workflow pipelines.
 */
export class PipelineFactory {
    private static workflows: Record<string, WorkflowFunction> = {};
    private static pipelines: Record<string, string[]> = {};

    /**
     * Register a custom workflow function.
     */
    static register(name: string, workflow: WorkflowFunction): void {
        this.workflows[name] = workflow;
    }

    /**
     * Register a dict of custom workflow functions.
     */
    static registerAll(workflows: Record<string, WorkflowFunction>): void {
        Object.entries(workflows).forEach(([name, workflow]) => {
            this.register(name, workflow);
        });
    }

    /**
     * Register a new pipeline method as a list of workflow names.
     */
    static registerPipeline(name: string, workflows: string[]): void {
        this.pipelines[name] = workflows;
    }

    /**
     * Create a pipeline generator.
     */
    static createPipeline(
        config: GraphRagConfig,
        method: IndexingMethod | string = IndexingMethod.Standard
    ): Pipeline {
        const workflows = config.workflows || this.pipelines[method] || [];
        logger.info("Creating pipeline with workflows:", workflows);
        
        const workflowTuples = workflows.map(name => {
            const workflow = this.workflows[name];
            if (!workflow) {
                throw new Error(`Workflow '${name}' not found`);
            }
            return [name, workflow] as [string, WorkflowFunction];
        });
        
        return new Pipeline(workflowTuples);
    }
}

// Register default implementations
const standardWorkflows = [
    "create_base_text_units",
    "create_final_documents",
    "extract_graph",
    "finalize_graph",
    "extract_covariates",
    "create_communities",
    "create_final_text_units",
    "create_community_reports",
    "generate_text_embeddings",
];

const fastWorkflows = [
    "create_base_text_units",
    "create_final_documents",
    "extract_graph_nlp",
    "prune_graph",
    "finalize_graph",
    "create_communities",
    "create_final_text_units",
    "create_community_reports_text",
    "generate_text_embeddings",
];

const updateWorkflows = [
    "update_final_documents",
    "update_entities_relationships",
    "update_text_units",
    "update_covariates",
    "update_communities",
    "update_community_reports",
    "update_text_embeddings",
    "update_clean_state",
];

PipelineFactory.registerPipeline(
    IndexingMethod.Standard, 
    ["load_input_documents", ...standardWorkflows]
);

PipelineFactory.registerPipeline(
    IndexingMethod.Fast, 
    ["load_input_documents", ...fastWorkflows]
);

PipelineFactory.registerPipeline(
    IndexingMethod.StandardUpdate,
    ["load_update_documents", ...standardWorkflows, ...updateWorkflows]
);

PipelineFactory.registerPipeline(
    IndexingMethod.FastUpdate,
    ["load_update_documents", ...fastWorkflows, ...updateWorkflows]
);
