﻿// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * <PERSON>til functions to retrieve community reports from a collection.
 */

import { CommunityReport }
import { Entity } from '../../../data_model/entity';

/**
 * DataFrame-like interface for TypeScript
 */
export interface DataFrame {
  [key: string]: any[];
  length: number;
}

/**
 * Get all communities that are related to selected entities.
 */
export function getCandidateCommunities(
  selectedEntities: Entity[],
  communityReports: CommunityReport[],
  includeCommunityRank: boolean = false,
  useCommunitySummary: boolean = false
): DataFrame {
  const selectedCommunityIds = selectedEntities
    .filter(entity => entity.communityIds)
    .flatMap(entity => entity.communityIds!);

  const selectedReports = communityReports.filter(community =>
    selectedCommunityIds.includes(community.id)
  );

  return toCommunityReportDataframe(
    selectedReports,
    includeCommunityRank,
    useCommunitySummary
  );
}

/**
 * Convert a list of communities to a pandas dataframe.
 */
export function toCommunityReportDataframe(
  reports: CommunityReport[],
  includeCommunityRank: boolean = false,
  useCommunitySummary: boolean = false
): DataFrame {
  if (reports.length === 0) {
    return { length: 0 };
  }

  // Add header
  const header = ['id', 'title'];
  const attributeCols = reports[0].attributes ? Object.keys(reports[0].attributes) : [];
  const filteredAttributeCols = attributeCols.filter(col => !header.includes(col));
  header.push(...filteredAttributeCols);
  header.push(useCommunitySummary ? 'summary' : 'content');
  
  if (includeCommunityRank) {
    header.push('rank');
  }

  const dataFrame: DataFrame = { length: reports.length };
  
  // Initialize columns
  header.forEach(col => {
    dataFrame[col] = [];
  });

  // Fill data
  reports.forEach(report => {
    dataFrame.id.push(report.shortId || '');
    dataFrame.title.push(report.title);
    
    // Add attribute columns
    filteredAttributeCols.forEach(field => {
      const value = report.attributes?.[field];
      dataFrame[field].push(value ? String(value) : '');
    });
    
    // Add content/summary
    const contentCol = useCommunitySummary ? 'summary' : 'content';
    dataFrame[contentCol].push(
      useCommunitySummary ? report.summary : report.fullContent
    );
    
    if (includeCommunityRank) {
      dataFrame.rank.push(String(report.rank || ''));
    }
  });

  return dataFrame;
}
