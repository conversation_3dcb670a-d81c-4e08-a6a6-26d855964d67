/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 * 
 * Azure CosmosDB Storage implementation of PipelineStorage.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { PipelineStorage, getTimestampFormattedWithLocalTz } from './pipeline-storage'

// CosmosDB SDK types (would be imported from @azure/cosmos in real implementation)
interface CosmosClient {
    createDatabaseIfNotExists(options: { id: string }): Promise<DatabaseResponse>
    deleteDatabase(database: DatabaseProxy): Promise<void>
}

interface DatabaseProxy {
    createContainerIfNotExists(options: { id: string, partitionKey: PartitionKey }): Promise<ContainerResponse>
    deleteContainer(container: ContainerProxy): Promise<void>
}

interface ContainerProxy {
    queryItems(options: { query: string, parameters?: any[], enableCrossPartitionQuery?: boolean }): AsyncIterableIterator<any>
    readItem(options: { item: string, partitionKey: string }): Promise<any>
    upsertItem(options: { body: any }): Promise<void>
    deleteItem(options: { item: string, partitionKey: string }): Promise<void>
}

interface DatabaseResponse {
    database: DatabaseProxy
}

interface ContainerResponse {
    container: ContainerProxy
}

interface PartitionKey {
    path: string
    kind: string
}

interface Progress {
    totalItems: number
    completedItems: number
    description: string
}

/**
 * The CosmosDB-Storage Implementation.
 */
export class CosmosDBPipelineStorage extends PipelineStorage {
    private _cosmosClient: any // Would be CosmosClient in real implementation
    private _databaseClient?: DatabaseProxy
    private _containerClient?: ContainerProxy
    private _cosmosdbAccountUrl?: string
    private _connectionString?: string
    private _databaseName: string
    private _containerName: string
    private _encoding: string
    private _noIdPrefixes: string[] = []
    private _cosmosdbAccountName?: string

    constructor(
        databaseName: string,
        containerName: string,
        cosmosdbAccountUrl?: string,
        connectionString?: string,
        encoding: string = "utf-8"
    ) {
        super()

        if (connectionString) {
            // In real implementation: this._cosmosClient = CosmosClient.fromConnectionString(connectionString)
            this._cosmosClient = this._createMockCosmosClient()
        } else {
            if (!cosmosdbAccountUrl) {
                throw new Error("Either connection_string or cosmosdb_account_url must be provided.")
            }
            // In real implementation: this._cosmosClient = new CosmosClient({ endpoint: cosmosdbAccountUrl, credential: new DefaultAzureCredential() })
            this._cosmosClient = this._createMockCosmosClient()
        }

        this._encoding = encoding
        this._databaseName = databaseName
        this._connectionString = connectionString
        this._cosmosdbAccountUrl = cosmosdbAccountUrl
        this._containerName = containerName
        this._cosmosdbAccountName = cosmosdbAccountUrl 
            ? cosmosdbAccountUrl.split("//")[1].split(".")[0]
            : undefined

        console.log(
            `Creating cosmosdb storage with account: ${this._cosmosdbAccountName} and database: ${this._databaseName} and container: ${this._containerName}`
        )

        this._createDatabase()
        this._createContainer()
    }

    private _createMockCosmosClient(): any {
        // Mock implementation for development/testing
        return {
            createDatabaseIfNotExists: async (options: { id: string }) => {
                console.log(`Mock: Creating database ${options.id}`)
                return {
                    database: {
                        createContainerIfNotExists: async (containerOptions: any) => {
                            console.log(`Mock: Creating container ${containerOptions.id}`)
                            return {
                                container: {
                                    queryItems: async function* (queryOptions: any) {
                                        // Mock empty query results
                                    },
                                    readItem: async (itemOptions: any) => {
                                        return { id: itemOptions.item, body: {}, _ts: Date.now() / 1000 }
                                    },
                                    upsertItem: async (upsertOptions: any) => {
                                        console.log(`Mock: Upserting item ${upsertOptions.body.id}`)
                                    },
                                    deleteItem: async (deleteOptions: any) => {
                                        console.log(`Mock: Deleting item ${deleteOptions.item}`)
                                    }
                                }
                            }
                        },
                        deleteContainer: async (container: any) => {
                            console.log("Mock: Deleting container")
                        }
                    }
                }
            },
            deleteDatabase: async (database: any) => {
                console.log("Mock: Deleting database")
            }
        }
    }

    private async _createDatabase(): Promise<void> {
        const response = await this._cosmosClient.createDatabaseIfNotExists({ id: this._databaseName })
        this._databaseClient = response.database
    }

    private async _deleteDatabase(): Promise<void> {
        if (this._databaseClient) {
            await this._cosmosClient.deleteDatabase(this._databaseClient)
            this._databaseClient = undefined
        }
        this._containerClient = undefined
    }

    private async _createContainer(): Promise<void> {
        const partitionKey: PartitionKey = { path: "/id", kind: "Hash" }
        if (this._databaseClient) {
            const response = await this._databaseClient.createContainerIfNotExists({
                id: this._containerName,
                partitionKey
            })
            this._containerClient = response.container
        }
    }

    private async _deleteContainer(): Promise<void> {
        if (this._databaseClient && this._containerClient) {
            await this._databaseClient.deleteContainer(this._containerClient)
            this._containerClient = undefined
        }
    }

    async* find(
        filePattern: RegExp,
        baseDir?: string,
        fileFilter?: Record<string, any>,
        maxCount: number = -1
    ): AsyncIterableIterator<[string, Record<string, any>]> {
        baseDir = baseDir || ""
        console.log(
            `Searching container ${this._containerName} for documents matching ${filePattern.source}`
        )

        if (!this._databaseClient || !this._containerClient) {
            return
        }

        const itemFilter = (item: Record<string, any>): boolean => {
            if (!fileFilter) return true
            return Object.entries(fileFilter).every(([key, value]) => {
                const regex = new RegExp(value)
                return regex.test(item[key] || "")
            })
        }

        try {
            let query = "SELECT * FROM c WHERE RegexMatch(c.id, @pattern)"
            const parameters: Array<{ name: string, value: any }> = [
                { name: "@pattern", value: filePattern.source }
            ]

            if (fileFilter) {
                Object.entries(fileFilter).forEach(([key, value]) => {
                    query += ` AND c.${key} = @${key}`
                    parameters.push({ name: `@${key}`, value })
                })
            }

            const items: any[] = []
            for await (const item of this._containerClient.queryItems({
                query,
                parameters,
                enableCrossPartitionQuery: true
            })) {
                items.push(item)
            }

            let numLoaded = 0
            const numTotal = items.length
            if (numTotal === 0) {
                return
            }
            let numFiltered = 0

            for (const item of items) {
                const match = filePattern.exec(item.id)
                if (match && match.groups) {
                    const group = match.groups
                    if (itemFilter(group)) {
                        yield [item.id, group]
                        numLoaded++
                        if (maxCount > 0 && numLoaded >= maxCount) {
                            break
                        }
                    } else {
                        numFiltered++
                    }
                } else {
                    numFiltered++
                }

                const progressStatus = this._createProgressStatus(numLoaded, numFiltered, numTotal)
                console.debug(
                    `Progress: ${progressStatus.description} (${progressStatus.completedItems}/${progressStatus.totalItems} completed)`
                )
            }
        } catch (error) {
            console.error("An error occurred while searching for documents in Cosmos DB.", error)
            throw error
        }
    }

    async get(key: string, asBytes?: boolean, encoding?: string): Promise<any> {
        try {
            if (!this._databaseClient || !this._containerClient) {
                return null
            }

            if (asBytes) {
                const prefix = this._getPrefix(key)
                const query = `SELECT * FROM c WHERE STARTSWITH(c.id, '${prefix}')`
                
                const items: any[] = []
                for await (const item of this._containerClient.queryItems({
                    query,
                    enableCrossPartitionQuery: true
                })) {
                    item.id = item.id.split(":")[1]
                    items.push(item)
                }

                // Drop the "id" column if the original dataframe does not include it
                if (this._noIdPrefixes.includes(prefix)) {
                    items.forEach(item => delete item.id)
                }

                // In real implementation, you would convert to parquet format
                // For now, return JSON string as bytes
                const jsonStr = JSON.stringify(items)
                return Buffer.from(jsonStr, encoding || this._encoding)
            }

            const item = await this._containerClient.readItem({ item: key, partitionKey: key })
            const itemBody = item.body
            return JSON.stringify(itemBody)
        } catch (error) {
            console.error(`Error reading item ${key}`, error)
            return null
        }
    }

    async set(key: string, value: any, encoding?: string): Promise<void> {
        try {
            if (!this._databaseClient || !this._containerClient) {
                throw new Error("Database or container not initialized")
            }

            // value represents a parquet file (as bytes)
            if (Buffer.isBuffer(value)) {
                const prefix = this._getPrefix(key)
                
                // In real implementation, you would use a parquet library to read the buffer
                // For now, assume it's JSON data
                const jsonStr = value.toString(encoding || this._encoding)
                let cosmosdbItemList: any[]
                
                try {
                    cosmosdbItemList = JSON.parse(jsonStr)
                } catch {
                    // If not JSON, treat as array with single item
                    cosmosdbItemList = [{ data: jsonStr }]
                }

                for (let index = 0; index < cosmosdbItemList.length; index++) {
                    const cosmosdbItem = cosmosdbItemList[index]
                    
                    // If the id key does not exist in the input dataframe json, create a unique id using the prefix and item index
                    let prefixedId: string
                    if (!cosmosdbItem.id) {
                        prefixedId = `${prefix}:${index}`
                        if (!this._noIdPrefixes.includes(prefix)) {
                            this._noIdPrefixes.push(prefix)
                        }
                    } else {
                        prefixedId = `${prefix}:${cosmosdbItem.id}`
                    }
                    
                    cosmosdbItem.id = prefixedId
                    await this._containerClient.upsertItem({ body: cosmosdbItem })
                }
            } else {
                // value represents a cache output or stats.json
                const cosmosdbItem = {
                    id: key,
                    body: typeof value === 'string' ? JSON.parse(value) : value
                }
                await this._containerClient.upsertItem({ body: cosmosdbItem })
            }
        } catch (error) {
            console.error(`Error writing item ${key}`, error)
            throw error
        }
    }

    async has(key: string): Promise<boolean> {
        if (!this._databaseClient || !this._containerClient) {
            return false
        }

        try {
            if (key.includes(".parquet")) {
                const prefix = this._getPrefix(key)
                const query = `SELECT * FROM c WHERE STARTSWITH(c.id, '${prefix}')`
                
                const items: any[] = []
                for await (const item of this._containerClient.queryItems({
                    query,
                    enableCrossPartitionQuery: true
                })) {
                    items.push(item)
                }
                return items.length > 0
            }

            const query = `SELECT * FROM c WHERE c.id = '${key}'`
            const items: any[] = []
            for await (const item of this._containerClient.queryItems({
                query,
                enableCrossPartitionQuery: true
            })) {
                items.push(item)
            }
            return items.length === 1
        } catch (error) {
            console.error(`Error checking if item ${key} exists`, error)
            return false
        }
    }

    async delete(key: string): Promise<void> {
        if (!this._databaseClient || !this._containerClient) {
            return
        }

        try {
            if (key.includes(".parquet")) {
                const prefix = this._getPrefix(key)
                const query = `SELECT * FROM c WHERE STARTSWITH(c.id, '${prefix}')`
                
                for await (const item of this._containerClient.queryItems({
                    query,
                    enableCrossPartitionQuery: true
                })) {
                    await this._containerClient.deleteItem({
                        item: item.id,
                        partitionKey: item.id
                    })
                }
            } else {
                await this._containerClient.deleteItem({
                    item: key,
                    partitionKey: key
                })
            }
        } catch (error) {
            // CosmosResourceNotFoundError equivalent - ignore if item doesn't exist
            if (error.code === 404) {
                return
            }
            console.error(`Error deleting item ${key}`, error)
            throw error
        }
    }

    async clear(): Promise<void> {
        // This currently deletes the database, including all containers and data within it.
        await this._deleteDatabase()
    }

    keys(): string[] {
        throw new Error("CosmosDB storage does not yet support listing keys.")
    }

    child(name?: string): PipelineStorage {
        return this
    }

    private _getPrefix(key: string): string {
        return key.split(".")[0]
    }

    async getCreationDate(key: string): Promise<string> {
        try {
            if (!this._databaseClient || !this._containerClient) {
                return ""
            }

            const item = await this._containerClient.readItem({ item: key, partitionKey: key })
            const timestamp = new Date(item._ts * 1000) // Convert from Unix timestamp
            return getTimestampFormattedWithLocalTz(timestamp)
        } catch (error) {
            console.error(`Error getting creation date for key ${key}`, error)
            return ""
        }
    }

    private _createProgressStatus(numLoaded: number, numFiltered: number, numTotal: number): Progress {
        return {
            totalItems: numTotal,
            completedItems: numLoaded + numFiltered,
            description: `${numLoaded} files loaded (${numFiltered} filtered)`
        }
    }
}

/**
 * Create a CosmosDB storage instance.
 */
export function createCosmosDbStorage(kwargs: Record<string, any>): PipelineStorage {
    console.log("Creating cosmosdb storage")
    
    const cosmosdbAccountUrl = kwargs.cosmosdb_account_url
    const connectionString = kwargs.connection_string
    const baseDir = kwargs.base_dir
    const containerName = kwargs.container_name

    if (!baseDir) {
        throw new Error("No base_dir provided for database name")
    }

    if (!connectionString && !cosmosdbAccountUrl) {
        throw new Error("connection_string or cosmosdb_account_url is required.")
    }

    return new CosmosDBPipelineStorage(
        baseDir,
        containerName,
        cosmosdbAccountUrl,
        connectionString
    )
}