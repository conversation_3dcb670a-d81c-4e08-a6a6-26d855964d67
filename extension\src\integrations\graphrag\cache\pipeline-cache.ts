// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * A module containing 'PipelineCache' model.
 */

/**
 * Provide a cache interface for the pipeline.
 */
export abstract class PipelineCache {
    /**
     * Get the value for the given key.
     * 
     * @param key - The key to get the value for.
     * @returns The value for the given key.
     */
    abstract get(key: string): Promise<any>;

    /**
     * Set the value for the given key.
     * 
     * @param key - The key to set the value for.
     * @param value - The value to set.
     * @param debugData - Optional debug data.
     */
    abstract set(key: string, value: any, debugData?: Record<string, any> | null): Promise<void>;

    /**
     * Return True if the given key exists in the cache.
     * 
     * @param key - The key to check for.
     * @returns True if the key exists in the cache, False otherwise.
     */
    abstract has(key: string): Promise<boolean>;

    /**
     * Delete the given key from the cache.
     * 
     * @param key - The key to delete.
     */
    abstract delete(key: string): Promise<void>;

    /**
     * Clear the cache.
     */
    abstract clear(): Promise<void>;

    /**
     * Create a child cache with the given name.
     * 
     * @param name - The name to create the sub cache with.
     */
    abstract child(name: string): PipelineCache;
}