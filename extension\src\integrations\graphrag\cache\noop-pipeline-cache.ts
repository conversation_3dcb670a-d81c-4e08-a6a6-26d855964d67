// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Module containing the NoopPipelineCache implementation.
 */

import { PipelineCache } from './pipeline-cache';

/**
 * A no-op implementation of the pipeline cache, usually useful for testing.
 */
export class NoopPipelineCache extends PipelineCache {
    /**
     * Get the value for the given key.
     * 
     * @param key - The key to get the value for.
     * @returns Always returns null.
     */
    async get(key: string): Promise<any> {
        return null;
    }

    /**
     * Set the value for the given key.
     * 
     * @param key - The key to set the value for.
     * @param value - The value to set.
     * @param debugData - Optional debug data.
     */
    async set(
        key: string,
        value: string | Buffer | null,
        debugData?: Record<string, any> | null
    ): Promise<void> {
        // No-op implementation
    }

    /**
     * Return True if the given key exists in the cache.
     * 
     * @param key - The key to check for.
     * @returns Always returns false.
     */
    async has(key: string): Promise<boolean> {
        return false;
    }

    /**
     * Delete the given key from the cache.
     * 
     * @param key - The key to delete.
     */
    async delete(key: string): Promise<void> {
        // No-op implementation
    }

    /**
     * Clear the cache.
     */
    async clear(): Promise<void> {
        // No-op implementation
    }

    /**
     * Create a child cache with the given name.
     * 
     * @param name - The name to create the sub cache with.
     */
    child(name: string): PipelineCache {
        return this;
    }
}