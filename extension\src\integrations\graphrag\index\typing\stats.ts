/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Pipeline stats types.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

/**
 * Pipeline running stats.
 * Matches the Python PipelineRunStats dataclass exactly.
 */
export interface PipelineRunStats {
    /** Float representing the total runtime */
    total_runtime: number;

    /** Number of documents */
    num_documents: number;

    /** Number of update documents */
    update_documents: number;

    /** Float representing the input load time */
    input_load_time: number;

    /** A dictionary of workflows */
    workflows: Record<string, Record<string, number>>;
}

/**
 * Create a new PipelineRunStats with default values.
 * Matches the Python PipelineRunStats dataclass field defaults exactly.
 */
export function createPipelineRunStats(): PipelineRunStats {
    return {
        total_runtime: 0,
        num_documents: 0,
        update_documents: 0,
        input_load_time: 0,
        workflows: {}
    };
}

// Constructor function that matches Python dataclass behavior
export class PipelineRunStats {
    total_runtime: number = 0;
    num_documents: number = 0;
    update_documents: number = 0;
    input_load_time: number = 0;
    workflows: Record<string, Record<string, number>> = {};

    constructor(
        total_runtime: number = 0,
        num_documents: number = 0,
        update_documents: number = 0,
        input_load_time: number = 0,
        workflows: Record<string, Record<string, number>> = {}
    ) {
        this.total_runtime = total_runtime;
        this.num_documents = num_documents;
        this.update_documents = update_documents;
        this.input_load_time = input_load_time;
        this.workflows = workflows;
    }
}