// Copyright (c) 2025 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Base llm protocol definitions.
 */

import { LanguageModelConfig } from '../../config/models/graph_rag_config';
import { ModelResponse } from '../response/base';

/**
 * Protocol for an embedding-based Language Model (LM).
 * 
 * This protocol defines the methods required for an embedding-based LM.
 */
export interface EmbeddingModel {
    /** Passthrough of the config used to create the model instance. */
    config: LanguageModelConfig;

    /**
     * Generate an embedding vector for the given list of strings.
     * 
     * @param textList - The list of texts to generate embeddings for.
     * @param kwargs - Additional keyword arguments (e.g., model parameters).
     * @returns A collection of list of floats representing the embedding vector for each item in the batch.
     */
    aembedBatch(textList: string[], kwargs?: Record<string, any>): Promise<number[][]>;

    /**
     * Generate an embedding vector for the given text.
     * 
     * @param text - The text to generate an embedding for.
     * @param kwargs - Additional keyword arguments (e.g., model parameters).
     * @returns A list of floats representing the embedding vector.
     */
    aembed(text: string, kwargs?: Record<string, any>): Promise<number[]>;

    /**
     * Generate an embedding vector for the given list of strings.
     * 
     * @param textList - The list of texts to generate embeddings for.
     * @param kwargs - Additional keyword arguments (e.g., model parameters).
     * @returns A collection of list of floats representing the embedding vector for each item in the batch.
     */
    embedBatch(textList: string[], kwargs?: Record<string, any>): number[][];

    /**
     * Generate an embedding vector for the given text.
     * 
     * @param text - The text to generate an embedding for.
     * @param kwargs - Additional keyword arguments (e.g., model parameters).
     * @returns A list of floats representing the embedding vector.
     */
    embed(text: string, kwargs?: Record<string, any>): number[];
}

/**
 * Protocol for a chat-based Language Model (LM).
 * 
 * This protocol defines the methods required for a chat-based LM.
 * Prompt is always required for the chat method, and any other keyword arguments are forwarded to the Model provider.
 */
export interface ChatModel {
    /** Passthrough of the config used to create the model instance. */
    config: LanguageModelConfig;

    /**
     * Generate a response for the given text.
     * 
     * @param prompt - The text to generate a response for.
     * @param history - The conversation history.
     * @param kwargs - Additional keyword arguments (e.g., model parameters).
     * @returns A ModelResponse representing the response.
     */
    achat(prompt: string, history?: any[] | null, kwargs?: Record<string, any>): Promise<ModelResponse>;

    /**
     * Generate a response for the given text using a streaming interface.
     * 
     * @param prompt - The text to generate a response for.
     * @param history - The conversation history.
     * @param kwargs - Additional keyword arguments (e.g., model parameters).
     * @returns An async generator that yields strings representing the response.
     */
    achatStream(prompt: string, history?: any[] | null, kwargs?: Record<string, any>): AsyncGenerator<string, void, unknown>;

    /**
     * Generate a response for the given text.
     * 
     * @param prompt - The text to generate a response for.
     * @param history - The conversation history.
     * @param kwargs - Additional keyword arguments (e.g., model parameters).
     * @returns A ModelResponse representing the response.
     */
    chat(prompt: string, history?: any[] | null, kwargs?: Record<string, any>): ModelResponse;

    /**
     * Generate a response for the given text using a streaming interface.
     * 
     * @param prompt - The text to generate a response for.
     * @param history - The conversation history.
     * @param kwargs - Additional keyword arguments (e.g., model parameters).
     * @returns A generator that yields strings representing the response.
     */
    chatStream(prompt: string, history?: any[] | null, kwargs?: Record<string, any>): Generator<string, void, unknown>;
}