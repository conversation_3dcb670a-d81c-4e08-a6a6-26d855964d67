﻿// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * The GlobalSearch Implementation.
 */

import { ChatModel } from '../../../language_model/protocol/base';
import { QueryCallbacks }
import { GlobalContextBuilder } from '../../context_builder/builders';
import { ConversationHistory }
import { numTokens, tryParseJsonObject }
import { BaseSearch, SearchResult } from '../base';
import { DataFrame }

const GENERAL_KNOWLEDGE_INSTRUCTION = `
The response may also include relevant real-world knowledge outside the dataset, but it must be explicitly annotated with a verification tag [LLM: verify]. For example:
"This is an example sentence supported by real-world knowledge [LLM: verify]."
`;

const MAP_SYSTEM_PROMPT = `
---Role---

You are a helpful assistant responding to questions about data in the tables provided.

---Goal---

Generate a comprehensive assessment of the provided data.
Given a question, extract all entities, relationships, and claims that are relevant to the question.
Format each point as a JSON object with the following format:
{
  "points": [
    {"description": "Point 1 description", "score": relevance_score_int},
    {"description": "Point 2 description", "score": relevance_score_int}
  ]
}

Where the relevance_score_int is an integer score between 1-100.

---Data---
{context_data}

---Target response length---
{max_length} words

Generate the response as a valid JSON object.
`;

const REDUCE_SYSTEM_PROMPT = `
---Role---

You are a helpful assistant responding to questions about data in the tables provided.

---Goal---

Generate a comprehensive response to the user's question based on the provided data.
The response should be well-structured, informative, and directly address the question.

---Target response length and format---

{response_type}

---Data---

{report_data}

---Instructions---

Generate a comprehensive response of {max_length} words that synthesizes the provided information.
Structure the response with clear sections and use markdown formatting.
Ensure all claims are supported by the provided data.
`;

const NO_DATA_ANSWER = `
I am sorry but I am unable to answer this question given the provided data.
`;

/**
 * A GlobalSearch result.
 */
export interface GlobalSearchResult extends SearchResult {
  mapResponses: SearchResult[];
  reduceContextData: string | DataFrame[] | Record<string, DataFrame>;
  reduceContextText: string | string[] | Record<string, string>;
}

/**
 * Search orchestration for global search mode.
 */
export class GlobalSearch extends BaseSearch<GlobalContextBuilder> {
  private mapSystemPrompt: string;
  private reduceSystemPrompt: string;
  private responseType: string;
  private allowGeneralKnowledge: boolean;
  private generalKnowledgeInclusionPrompt: string;
  private callbacks: QueryCallbacks[];
  private maxDataTokens: number;
  private mapLlmParams: Record<string, any>;
  private reduceLlmParams: Record<string, any>;
  private mapMaxLength: number;
  private reduceMaxLength: number;
  private semaphore: { acquire: () => Promise<void>; release: () => void };

  constructor(
    model: ChatModel,
    contextBuilder: GlobalContextBuilder,
    tokenEncoder?: any,
    mapSystemPrompt?: string,
    reduceSystemPrompt?: string,
    responseType: string = 'multiple paragraphs',
    allowGeneralKnowledge: boolean = false,
    generalKnowledgeInclusionPrompt?: string,
    jsonMode: boolean = true,
    callbacks?: QueryCallbacks[],
    maxDataTokens: number = 8000,
    mapLlmParams?: Record<string, any>,
    reduceLlmParams?: Record<string, any>,
    mapMaxLength: number = 1000,
    reduceMaxLength: number = 2000,
    contextBuilderParams?: Record<string, any>,
    concurrentCoroutines: number = 32
  ) {
    super(
      model,
      contextBuilder,
      tokenEncoder,
      undefined,
      contextBuilderParams
    );
    
    this.mapSystemPrompt = mapSystemPrompt || MAP_SYSTEM_PROMPT;
    this.reduceSystemPrompt = reduceSystemPrompt || REDUCE_SYSTEM_PROMPT;
    this.responseType = responseType;
    this.allowGeneralKnowledge = allowGeneralKnowledge;
    this.generalKnowledgeInclusionPrompt = generalKnowledgeInclusionPrompt || GENERAL_KNOWLEDGE_INSTRUCTION;
    this.callbacks = callbacks || [];
    this.maxDataTokens = maxDataTokens;

    this.mapLlmParams = mapLlmParams || {};
    this.reduceLlmParams = reduceLlmParams || {};
    
    if (jsonMode) {
      this.mapLlmParams.response_format = { type: 'json_object' };
    } else {
      delete this.mapLlmParams.response_format;
    }
    
    this.mapMaxLength = mapMaxLength;
    this.reduceMaxLength = reduceMaxLength;

    // Simple semaphore implementation
    let activeCount = 0;
    const waitingQueue: (() => void)[] = [];
    
    this.semaphore = {
      acquire: async () => {
        return new Promise<void>((resolve) => {
          if (activeCount < concurrentCoroutines) {
            activeCount++;
            resolve();
          } else {
            waitingQueue.push(() => {
              activeCount++;
              resolve();
            });
          }
        });
      },
      release: () => {
        activeCount--;
        if (waitingQueue.length > 0) {
          const next = waitingQueue.shift();
          if (next) next();
        }
      }
    };
  }

  async *streamSearch(
    query: string,
    conversationHistory?: ConversationHistory
  ): AsyncGenerator<string, void, unknown> {
    /**
     * Stream the global search response.
     */
    const contextResult = await this.contextBuilder.buildContext(
      query,
      conversationHistory,
      this.contextBuilderParams
    );

    for (const callback of this.callbacks) {
      callback.onMapResponseStart(contextResult.contextChunks as string[]);
    }

    const mapResponses = await Promise.all(
      (contextResult.contextChunks as string[]).map(data =>
        this.mapResponseSingleBatch(
          data,
          query,
          this.mapMaxLength,
          this.mapLlmParams
        )
      )
    );

    for (const callback of this.callbacks) {
      callback.onMapResponseEnd(mapResponses);
      callback.onContext(contextResult.contextRecords);
    }

    for await (const response of this.streamReduceResponse(
      mapResponses,
      query,
      this.reduceMaxLength,
      this.reduceLlmParams
    )) {
      yield response;
    }
  }

  async search(
    query: string,
    conversationHistory?: ConversationHistory,
    ...kwargs: any[]
  ): Promise<GlobalSearchResult> {
    /**
     * Perform a global search.
     * 
     * Global search mode includes two steps:
     * - Step 1: Run parallel LLM calls on communities' short summaries to generate answer for each batch
     * - Step 2: Combine the answers from step 2 to generate the final answer
     */
    const llmCalls: Record<string, number> = {};
    const promptTokens: Record<string, number> = {};
    const outputTokens: Record<string, number> = {};

    const startTime = Date.now();
    const contextResult = await this.contextBuilder.buildContext(
      query,
      conversationHistory,
      this.contextBuilderParams
    );

    llmCalls.build_context = contextResult.llmCalls;
    promptTokens.build_context = contextResult.promptTokens;
    outputTokens.build_context = contextResult.outputTokens;

    for (const callback of this.callbacks) {
      callback.onMapResponseStart(contextResult.contextChunks as string[]);
    }

    const mapResponses = await Promise.all(
      (contextResult.contextChunks as string[]).map(data =>
        this.mapResponseSingleBatch(
          data,
          query,
          this.mapMaxLength,
          this.mapLlmParams
        )
      )
    );

    for (const callback of this.callbacks) {
      callback.onMapResponseEnd(mapResponses);
      callback.onContext(contextResult.contextRecords);
    }

    llmCalls.map = mapResponses.reduce((sum, response) => sum + response.llmCalls, 0);
    promptTokens.map = mapResponses.reduce((sum, response) => sum + response.promptTokens, 0);
    outputTokens.map = mapResponses.reduce((sum, response) => sum + response.outputTokens, 0);

    // Step 2: Combine the intermediate answers from step 2 to generate the final answer
    const reduceResponse = await this.reduceResponse(
      mapResponses,
      query,
      this.reduceLlmParams
    );

    llmCalls.reduce = reduceResponse.llmCalls;
    promptTokens.reduce = reduceResponse.promptTokens;
    outputTokens.reduce = reduceResponse.outputTokens;

    return {
      response: reduceResponse.response,
      contextData: contextResult.contextRecords,
      contextText: contextResult.contextChunks,
      mapResponses,
      reduceContextData: reduceResponse.contextData,
      reduceContextText: reduceResponse.contextText,
      completionTime: Date.now() - startTime,
      llmCalls: Object.values(llmCalls).reduce((sum, val) => sum + val, 0),
      promptTokens: Object.values(promptTokens).reduce((sum, val) => sum + val, 0),
      outputTokens: Object.values(outputTokens).reduce((sum, val) => sum + val, 0),
      llmCallsCategories: llmCalls,
      promptTokensCategories: promptTokens,
      outputTokensCategories: outputTokens
    };
  }

  private async mapResponseSingleBatch(
    contextData: string,
    query: string,
    maxLength: number,
    llmKwargs: Record<string, any>
  ): Promise<SearchResult> {
    /**
     * Generate answer for a single chunk of community reports.
     */
    const startTime = Date.now();
    let searchPrompt = '';
    
    try {
      searchPrompt = this.mapSystemPrompt
        .replace('{context_data}', contextData)
        .replace('{max_length}', maxLength.toString());

      const searchMessages = [
        { role: 'system' as const, content: searchPrompt }
      ];

      await this.semaphore.acquire();
      try {
        const modelResponse = await this.model.achat(
          query,
          searchMessages,
          { ...llmKwargs, json: true }
        );
        const searchResponse = modelResponse.output.content;
        console.debug('Map response:', searchResponse);

        let processedResponse: any[];
        try {
          processedResponse = this.parseSearchResponse(searchResponse);
        } catch (error) {
          console.warn('Warning: Error parsing search response json - skipping this batch');
          processedResponse = [];
        }

        return {
          response: processedResponse,
          contextData,
          contextText: contextData,
          completionTime: Date.now() - startTime,
          llmCalls: 1,
          promptTokens: numTokens(searchPrompt, this.tokenEncoder),
          outputTokens: numTokens(searchResponse, this.tokenEncoder)
        };
      } finally {
        this.semaphore.release();
      }

    } catch (error) {
      console.error('Exception in _map_response_single_batch', error);
      return {
        response: [{ answer: '', score: 0 }],
        contextData,
        contextText: contextData,
        completionTime: Date.now() - startTime,
        llmCalls: 1,
        promptTokens: numTokens(searchPrompt, this.tokenEncoder),
        outputTokens: 0
      };
    }
  }

  private parseSearchResponse(searchResponse: string): Array<{ answer: string; score: number }> {
    /**
     * Parse the search response json and return a list of key points.
     */
    const [, parsed] = tryParseJsonObject(searchResponse);
    if (Object.keys(parsed).length === 0) {
      return [{ answer: '', score: 0 }];
    }

    const parsedElements = parsed.points;
    if (!parsedElements || !Array.isArray(parsedElements)) {
      return [{ answer: '', score: 0 }];
    }

    return parsedElements
      .filter(element => 'description' in element && 'score' in element)
      .map(element => ({
        answer: element.description,
        score: parseInt(element.score, 10)
      }));
  }

  private async reduceResponse(
    mapResponses: SearchResult[],
    query: string,
    llmKwargs: Record<string, any>
  ): Promise<SearchResult> {
    /**
     * Combine all intermediate responses from single batches into a final answer to the user query.
     */
    let textData = '';
    let searchPrompt = '';
    const startTime = Date.now();

    try {
      // collect all key points into a single list to prepare for sorting
      const keyPoints: Array<{ analyst: number; answer: string; score: number }> = [];
      
      for (const [index, response] of mapResponses.entries()) {
        if (!Array.isArray(response.response)) continue;
        
        for (const element of response.response) {
          if (typeof element !== 'object' || !element) continue;
          if (!('answer' in element) || !('score' in element)) continue;
          
          keyPoints.push({
            analyst: index,
            answer: element.answer,
            score: element.score
          });
        }
      }

      // filter response with score = 0 and rank responses by descending order of score
      const filteredKeyPoints = keyPoints.filter(point => point.score > 0);

      if (filteredKeyPoints.length === 0 && !this.allowGeneralKnowledge) {
        console.warn(
          'Warning: All map responses have score 0 (i.e., no relevant information found from the dataset), returning a canned "I do not know" answer.'
        );
        return {
          response: NO_DATA_ANSWER,
          contextData: '',
          contextText: '',
          completionTime: Date.now() - startTime,
          llmCalls: 0,
          promptTokens: 0,
          outputTokens: 0
        };
      }

      const sortedKeyPoints = filteredKeyPoints.sort((a, b) => b.score - a.score);

      const data: string[] = [];
      let totalTokens = 0;
      
      for (const point of sortedKeyPoints) {
        const formattedResponseData = [
          `----Analyst ${point.analyst + 1}----`,
          `Importance Score: ${point.score}`,
          point.answer
        ];
        const formattedResponseText = formattedResponseData.join('\n');
        
        if (totalTokens + numTokens(formattedResponseText, this.tokenEncoder) > this.maxDataTokens) {
          break;
        }
        
        data.push(formattedResponseText);
        totalTokens += numTokens(formattedResponseText, this.tokenEncoder);
      }
      
      textData = data.join('\n\n');

      searchPrompt = this.reduceSystemPrompt
        .replace('{report_data}', textData)
        .replace('{response_type}', this.responseType)
        .replace('{max_length}', this.reduceMaxLength.toString());

      if (this.allowGeneralKnowledge) {
        searchPrompt += '\n' + this.generalKnowledgeInclusionPrompt;
      }

      const searchMessages = [
        { role: 'system' as const, content: searchPrompt },
        { role: 'user' as const, content: query }
      ];

      let searchResponse = '';
      const stream = this.model.achatStream(query, searchMessages, llmKwargs);

      for await (const chunkResponse of stream) {
        searchResponse += chunkResponse;
        for (const callback of this.callbacks) {
          callback.onLlmNewToken(chunkResponse);
        }
      }

      return {
        response: searchResponse,
        contextData: textData,
        contextText: textData,
        completionTime: Date.now() - startTime,
        llmCalls: 1,
        promptTokens: numTokens(searchPrompt, this.tokenEncoder),
        outputTokens: numTokens(searchResponse, this.tokenEncoder)
      };

    } catch (error) {
      console.error('Exception in reduce_response', error);
      return {
        response: '',
        contextData: textData,
        contextText: textData,
        completionTime: Date.now() - startTime,
        llmCalls: 1,
        promptTokens: numTokens(searchPrompt, this.tokenEncoder),
        outputTokens: 0
      };
    }
  }

  private async *streamReduceResponse(
    mapResponses: SearchResult[],
    query: string,
    maxLength: number,
    llmKwargs: Record<string, any>
  ): AsyncGenerator<string, void, unknown> {
    // collect all key points into a single list to prepare for sorting
    const keyPoints: Array<{ analyst: number; answer: string; score: number }> = [];
    
    for (const [index, response] of mapResponses.entries()) {
      if (!Array.isArray(response.response)) continue;
      
      for (const element of response.response) {
        if (typeof element !== 'object' || !element) continue;
        if (!('answer' in element) || !('score' in element)) continue;
        
        keyPoints.push({
          analyst: index,
          answer: element.answer,
          score: element.score
        });
      }
    }

    // filter response with score = 0 and rank responses by descending order of score
    const filteredKeyPoints = keyPoints.filter(point => point.score > 0);

    if (filteredKeyPoints.length === 0 && !this.allowGeneralKnowledge) {
      console.warn(
        'Warning: All map responses have score 0 (i.e., no relevant information found from the dataset), returning a canned "I do not know" answer.'
      );
      yield NO_DATA_ANSWER;
      return;
    }

    const sortedKeyPoints = filteredKeyPoints.sort((a, b) => b.score - a.score);

    const data: string[] = [];
    let totalTokens = 0;
    
    for (const point of sortedKeyPoints) {
      const formattedResponseData = [
        `----Analyst ${point.analyst + 1}----`,
        `Importance Score: ${point.score}`,
        point.answer
      ];
      const formattedResponseText = formattedResponseData.join('\n');
      
      if (totalTokens + numTokens(formattedResponseText, this.tokenEncoder) > this.maxDataTokens) {
        break;
      }
      
      data.push(formattedResponseText);
      totalTokens += numTokens(formattedResponseText, this.tokenEncoder);
    }
    
    const textData = data.join('\n\n');

    let searchPrompt = this.reduceSystemPrompt
      .replace('{report_data}', textData)
      .replace('{response_type}', this.responseType)
      .replace('{max_length}', maxLength.toString());

    if (this.allowGeneralKnowledge) {
      searchPrompt += '\n' + this.generalKnowledgeInclusionPrompt;
    }

    const searchMessages = [
      { role: 'system' as const, content: searchPrompt }
    ];

    const stream = this.model.achatStream(query, searchMessages, llmKwargs);

    for await (const chunkResponse of stream) {
      for (const callback of this.callbacks) {
        callback.onLlmNewToken(chunkResponse);
      }
      yield chunkResponse;
    }
  }
}
