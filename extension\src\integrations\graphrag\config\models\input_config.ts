﻿// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Parameterization settings for the input configuration.
 */

import { graphragConfigDefaults } from '../defaults.js';
import { InputFileType } from '../enums.js';
import { StorageConfig, createStorageConfig } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;

/**
 * The configuration section for Input.
 */
export interface InputConfig {
  /**
   * The storage configuration to use for reading input documents.
   */
  storage: StorageConfig;

  /**
   * The input file type to use.
   */
  fileType: InputFileType;

  /**
   * The input file encoding to use.
   */
  encoding: string;

  /**
   * The input file pattern to use.
   */
  filePattern: string;

  /**
   * The optional file filter for the input files.
   */
  fileFilter?: Record<string, string>;

  /**
   * The input text column to use.
   */
  textColumn: string;

  /**
   * The input title column to use.
   */
  titleColumn?: string;

  /**
   * The document attribute columns to use.
   */
  metadata?: string[];
}

/**
 * Create an InputConfig with default values.
 */
export function createInputConfig(config: Partial<InputConfig> = {}): InputConfig {
  return {
    storage: config.storage ?? createStorageConfig({
      baseDir: graphragConfigDefaults.input.storage.baseDir,
    }),
    fileType: config.fileType ?? graphragConfigDefaults.input.fileType,
    encoding: config.encoding ?? graphragConfigDefaults.input.encoding,
    filePattern: config.filePattern ?? graphragConfigDefaults.input.filePattern,
    fileFilter: config.fileFilter ?? graphragConfigDefaults.input.fileFilter,
    textColumn: config.textColumn ?? graphragConfigDefaults.input.textColumn,
    titleColumn: config.titleColumn ?? graphragConfigDefaults.input.titleColumn,
    metadata: config.metadata ?? graphragConfigDefaults.input.metadata,
  };
}
