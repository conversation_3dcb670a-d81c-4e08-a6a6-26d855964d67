/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing the 'Tokenizer', 'TextSplitter', 'NoopTextSplitter' and 'TokenTextSplitter' models.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

// Note: tiktoken would be imported in a real implementation
// import * as tiktoken from 'tiktoken';
// import { TextChunk } from '../operations/chunk_text/typing.js';
// import { ProgressTicker } from '../../logger/progress.js';
// import * as defs from '../../config/defaults.js';

const logger = console;

// Python: EncodedText = list[int]
export type EncodedText = number[];
// Python: DecodeFn = Callable[[EncodedText], str]
export type DecodeFn = (encoded: EncodedText) => string;
// Python: EncodeFn = Callable[[str], EncodedText]
export type EncodeFn = (text: string) => EncodedText;
// Python: LengthFn = Callable[[str], int]
export type LengthFn = (text: string) => number;

/**
 * Tokenizer data class.
 * Matches the Python Tokenizer dataclass exactly.
 */
export interface Tokenizer {
    /** Overlap in tokens between chunks */
    chunk_overlap: number;
    /** Maximum number of tokens per chunk */
    tokens_per_chunk: number;
    /** Function to decode a list of token ids to a string */
    decode: DecodeFn;
    /** Function to encode a string to a list of token ids */
    encode: EncodeFn;
}

/**
 * Text chunk with metadata.
 * Matches the Python TextChunk dataclass exactly.
 */
export interface TextChunk {
    text: string;
    doc_indices: number[];
    token_count: number;
}

/**
 * Text splitter configuration.
 */
export interface TextSplitterConfig {
    chunkSize?: number;
    chunkOverlap?: number;
    lengthFunction?: LengthFn;
    keepSeparator?: boolean;
    addStartIndex?: boolean;
    stripWhitespace?: boolean;
}

/**
 * Abstract text splitter class definition.
 */
export abstract class TextSplitter {
    protected chunkSize: number;
    protected chunkOverlap: number;
    protected lengthFunction: LengthFn;
    protected keepSeparator: boolean;
    protected addStartIndex: boolean;
    protected stripWhitespace: boolean;

    constructor(config: TextSplitterConfig = {}) {
        // Based on text-ada-002-embedding max input buffer length
        // https://platform.openai.com/docs/guides/embeddings/second-generation-models
        this.chunkSize = config.chunkSize ?? 8191;
        this.chunkOverlap = config.chunkOverlap ?? 100;
        this.lengthFunction = config.lengthFunction ?? ((text: string) => text.length);
        this.keepSeparator = config.keepSeparator ?? false;
        this.addStartIndex = config.addStartIndex ?? false;
        this.stripWhitespace = config.stripWhitespace ?? true;
    }

    /**
     * Split text method definition.
     */
    abstract splitText(text: string | string[]): string[];
}

/**
 * Noop text splitter class definition.
 */
export class NoopTextSplitter extends TextSplitter {
    /**
     * Split text method definition.
     */
    splitText(text: string | string[]): string[] {
        return typeof text === 'string' ? [text] : text;
    }
}

/**
 * Token text splitter configuration.
 */
export interface TokenTextSplitterConfig extends TextSplitterConfig {
    encodingName?: string;
    modelName?: string;
    allowedSpecial?: 'all' | Set<string>;
    disallowedSpecial?: 'all' | string[];
}

/**
 * Token text splitter class definition.
 */
export class TokenTextSplitter extends TextSplitter {
    private encodingName: string;
    private modelName?: string;
    private allowedSpecial: 'all' | Set<string>;
    private disallowedSpecial: 'all' | string[];

    constructor(config: TokenTextSplitterConfig = {}) {
        super(config);
        this.encodingName = config.encodingName ?? 'cl100k_base';
        this.modelName = config.modelName;
        this.allowedSpecial = config.allowedSpecial ?? new Set();
        this.disallowedSpecial = config.disallowedSpecial ?? 'all';
    }

    /**
     * Encode the given text into an int-vector.
     * Matches the Python encode method exactly.
     */
    encode(text: string): number[] {
        // Python: return self._tokenizer.encode(text, allowed_special=self._allowed_special, disallowed_special=self._disallowed_special)
        // Note: In production, you'd use tiktoken with the actual encoding
        // For now, we'll use a simple approximation based on character codes
        const tokens: number[] = [];
        for (let i = 0; i < text.length; i++) {
            tokens.push(text.charCodeAt(i));
        }
        return tokens;
    }

    /**
     * Decode token IDs back to text.
     * Matches the Python decode method exactly.
     */
    decode(tokens: number[]): string {
        // Python: return self._tokenizer.decode(tokens)
        // Note: In production, you'd use tiktoken with the actual decoding
        // For now, we'll reverse the simple encoding
        return tokens.map(token => String.fromCharCode(token)).join('');
    }

    /**
     * Return the number of tokens in a string.
     * Matches the Python num_tokens method exactly.
     */
    num_tokens(text: string): number {
        // Python: return len(self.encode(text))
        return this.encode(text).length;
    }

    /**
     * Split text method.
     * Matches the Python split_text method exactly.
     */
    split_text(text: string | string[]): string[] {
        let textToSplit: string;
        
        if (Array.isArray(text)) {
            textToSplit = text.join(' ');
        } else if (text == null || text === '') {
            return [];
        } else if (typeof text !== 'string') {
            throw new TypeError(`Attempting to split a non-string value, actual is ${typeof text}`);
        } else {
            textToSplit = text;
        }

        const tokenizer: Tokenizer = {
            chunk_overlap: this.chunkOverlap,
            tokens_per_chunk: this.chunkSize,
            decode: (tokens) => this.decode(tokens),
            encode: (text) => this.encode(text),
        };

        return split_single_text_on_tokens(textToSplit, tokenizer);
    }

    // Compatibility method for existing code
    splitText(text: string | string[]): string[] {
        return this.split_text(text);
    }
}

/**
 * Split a single text and return chunks using the tokenizer.
 * Matches the Python split_single_text_on_tokens function exactly.
 */
export function split_single_text_on_tokens(text: string, tokenizer: Tokenizer): string[] {
    const result: string[] = [];
    const inputIds = tokenizer.encode(text);

    let startIdx = 0;
    let curIdx = Math.min(startIdx + tokenizer.tokens_per_chunk, inputIds.length);
    let chunkIds = inputIds.slice(startIdx, curIdx);

    while (startIdx < inputIds.length) {
        const chunkText = tokenizer.decode(chunkIds);
        result.push(chunkText);

        if (curIdx === inputIds.length) {
            break;
        }

        startIdx += tokenizer.tokens_per_chunk - tokenizer.chunk_overlap;
        curIdx = Math.min(startIdx + tokenizer.tokens_per_chunk, inputIds.length);
        chunkIds = inputIds.slice(startIdx, curIdx);
    }

    return result;
}

/**
 * Split multiple texts and return chunks with metadata using the tokenizer.
 * Matches the Python split_multiple_texts_on_tokens function exactly.
 * Adapted from langchain implementation for better control over the chunking process.
 */
export function split_multiple_texts_on_tokens(
    texts: string[],
    tokenizer: Tokenizer,
    tick?: (increment: number) => void
): TextChunk[] {
    const result: TextChunk[] = [];
    const mappedIds: Array<[number, number[]]> = [];

    // Encode all texts
    for (let sourceDocIdx = 0; sourceDocIdx < texts.length; sourceDocIdx++) {
        const text = texts[sourceDocIdx];
        const encoded = tokenizer.encode(text);
        if (tick) {
            tick(1); // Track progress if tick callback is provided
        }
        mappedIds.push([sourceDocIdx, encoded]);
    }

    // Flatten all token IDs with source document indices
    const inputIds: Array<[number, number]> = [];
    for (const [sourceDocIdx, ids] of mappedIds) {
        for (const id of ids) {
            inputIds.push([sourceDocIdx, id]);
        }
    }

    let startIdx = 0;
    let curIdx = Math.min(startIdx + tokenizer.tokens_per_chunk, inputIds.length);
    let chunkIds = inputIds.slice(startIdx, curIdx);

    while (startIdx < inputIds.length) {
        const tokenIds = chunkIds.map(([_, id]) => id);
        const chunkText = tokenizer.decode(tokenIds);
        const docIndices = Array.from(new Set(chunkIds.map(([docIdx, _]) => docIdx)));

        result.push({
            text: chunkText,
            doc_indices: docIndices,
            token_count: chunkIds.length
        });

        if (curIdx === inputIds.length) {
            break;
        }

        startIdx += tokenizer.tokens_per_chunk - tokenizer.chunk_overlap;
        curIdx = Math.min(startIdx + tokenizer.tokens_per_chunk, inputIds.length);
        chunkIds = inputIds.slice(startIdx, curIdx);
    }

    return result;
}

// Compatibility exports for existing code
export const splitSingleTextOnTokens = split_single_text_on_tokens;
export const splitMultipleTextsOnTokens = split_multiple_texts_on_tokens;