/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 * 
 * A module containing 'PipelineStorage' model.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

/**
 * Provide a storage interface for the pipeline. This is where the pipeline will store its output data.
 */
export abstract class PipelineStorage {
    /**
     * Find files in the storage using a file pattern, as well as a custom filter function.
     */
    abstract find(
        filePattern: RegExp,
        baseDir?: string,
        fileFilter?: Record<string, any>,
        maxCount?: number
    ): AsyncIterableIterator<[string, Record<string, any>]>

    /**
     * Get the value for the given key.
     * 
     * @param key - The key to get the value for.
     * @param asBytes - Whether or not to return the value as bytes.
     * @param encoding - The encoding to use when reading the value.
     * @returns The value for the given key.
     */
    abstract get(
        key: string, 
        asBytes?: boolean, 
        encoding?: string
    ): Promise<any>

    /**
     * Set the value for the given key.
     * 
     * @param key - The key to set the value for.
     * @param value - The value to set.
     * @param encoding - The encoding to use when writing the value.
     */
    abstract set(
        key: string, 
        value: any, 
        encoding?: string
    ): Promise<void>

    /**
     * Return True if the given key exists in the storage.
     * 
     * @param key - The key to check for.
     * @returns True if the key exists in the storage, False otherwise.
     */
    abstract has(key: string): Promise<boolean>

    /**
     * Delete the given key from the storage.
     * 
     * @param key - The key to delete.
     */
    abstract delete(key: string): Promise<void>

    /**
     * Clear the storage.
     */
    abstract clear(): Promise<void>

    /**
     * Create a child storage instance.
     */
    abstract child(name?: string): PipelineStorage

    /**
     * List all keys in the storage.
     */
    abstract keys(): string[]

    /**
     * Get the creation date for the given key.
     * 
     * @param key - The key to get the creation date for.
     * @returns The creation date for the given key.
     */
    abstract getCreationDate(key: string): Promise<string>
}

/**
 * Get the formatted timestamp with the local time zone.
 */
export function getTimestampFormattedWithLocalTz(timestamp: Date): string {
    // Format: "YYYY-MM-DD HH:MM:SS +ZZZZ"
    const year = timestamp.getFullYear()
    const month = String(timestamp.getMonth() + 1).padStart(2, '0')
    const day = String(timestamp.getDate()).padStart(2, '0')
    const hours = String(timestamp.getHours()).padStart(2, '0')
    const minutes = String(timestamp.getMinutes()).padStart(2, '0')
    const seconds = String(timestamp.getSeconds()).padStart(2, '0')
    
    // Get timezone offset in format +HHMM or -HHMM
    const timezoneOffset = timestamp.getTimezoneOffset()
    const offsetHours = Math.floor(Math.abs(timezoneOffset) / 60)
    const offsetMinutes = Math.abs(timezoneOffset) % 60
    const offsetSign = timezoneOffset <= 0 ? '+' : '-'
    const offsetString = `${offsetSign}${String(offsetHours).padStart(2, '0')}${String(offsetMinutes).padStart(2, '0')}`
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds} ${offsetString}`
}