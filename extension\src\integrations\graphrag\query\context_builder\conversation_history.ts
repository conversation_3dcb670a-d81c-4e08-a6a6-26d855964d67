﻿/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Classes for storing and managing conversation history.
 */

import { get_encoding } from 'tiktoken';
import { numTokens } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;

export enum ConversationRole {
    SYSTEM = "system",
    USER = "user",
    ASSISTANT = "assistant",
}

export function conversationRoleFromString(value: string): ConversationRole {
    switch (value) {
        case "system":
            return ConversationRole.SYSTEM;
        case "user":
            return ConversationRole.USER;
        case "assistant":
            return ConversationRole.ASSISTANT;
        default:
            throw new Error(`Invalid Role: ${value}`);
    }
}

export interface ConversationTurn {
    role: ConversationRole;
    content: string;
}

export interface QATurn {
    userQuery: ConversationTurn;
    assistantAnswers?: ConversationTurn[];
}

export class ConversationHistory {
    public turns: ConversationTurn[] = [];

    constructor() {
        this.turns = [];
    }

    static fromList(conversationTurns: Array<{ role: string; content: string }>): ConversationHistory {
        const history = new ConversationHistory();
        for (const turn of conversationTurns) {
            history.turns.push({
                role: conversationRoleFromString(turn.role || ConversationRole.USER),
                content: turn.content || "",
            });
        }
        return history;
    }

    addTurn(role: ConversationRole, content: string): void {
        this.turns.push({ role, content });
    }

    toQATurns(): QATurn[] {
        const qaTurns: QATurn[] = [];
        let currentQATurn: QATurn | null = null;

        for (const turn of this.turns) {
            if (turn.role === ConversationRole.USER) {
                if (currentQATurn) {
                    qaTurns.push(currentQATurn);
                }
                currentQATurn = { userQuery: turn, assistantAnswers: [] };
            } else {
                if (currentQATurn) {
                    if (!currentQATurn.assistantAnswers) {
                        currentQATurn.assistantAnswers = [];
                    }
                    currentQATurn.assistantAnswers.push(turn);
                }
            }
        }

        if (currentQATurn) {
            qaTurns.push(currentQATurn);
        }

        return qaTurns;
    }

    getUserTurns(maxUserTurns: number = 1): string[] {
        const userTurns: string[] = [];
        for (let i = this.turns.length - 1; i >= 0; i--) {
            const turn = this.turns[i];
            if (turn.role === ConversationRole.USER) {
                userTurns.push(turn.content);
                if (userTurns.length >= maxUserTurns) {
                    break;
                }
            }
        }
        return userTurns;
    }

    buildContext(options: {
        tokenEncoder?: any;
        includeUserTurnsOnly?: boolean;
        maxQATurns?: number;
        maxContextTokens?: number;
        recencyBias?: boolean;
        columnDelimiter?: string;
        contextName?: string;
    } = {}): [string, Record<string, any>] {
        const {
            tokenEncoder,
            includeUserTurnsOnly = true,
            maxQATurns = 5,
            maxContextTokens = 8000,
            recencyBias = true,
            columnDelimiter = "|",
            contextName = "Conversation History"
        } = options;

        let qaTurns = this.toQATurns();

        if (includeUserTurnsOnly) {
            qaTurns = qaTurns.map(qaTurn => ({
                userQuery: qaTurn.userQuery,
                assistantAnswers: undefined
            }));
        }

        if (recencyBias) {
            qaTurns = qaTurns.reverse();
        }

        if (maxQATurns && qaTurns.length > maxQATurns) {
            qaTurns = qaTurns.slice(0, maxQATurns);
        }

        if (qaTurns.length === 0) {
            return ["", { [contextName]: [] }];
        }

        const header = `-----${contextName}-----\n`;
        const turnList: Array<{ turn: string; content: string }> = [];
        let currentContextDF: Array<{ turn: string; content: string }> = [];

        for (const turn of qaTurns) {
            turnList.push({
                turn: ConversationRole.USER.toString(),
                content: turn.userQuery.content,
            });

            if (turn.assistantAnswers) {
                const answerText = turn.assistantAnswers.map(answer => answer.content).join("\n");
                turnList.push({
                    turn: ConversationRole.ASSISTANT.toString(),
                    content: answerText,
                });
            }

            const contextText = header + this.arrayToCSV(turnList, columnDelimiter);
            if (numTokens(contextText, tokenEncoder) > maxContextTokens) {
                break;
            }

            currentContextDF = [...turnList];
        }

        const contextText = header + this.arrayToCSV(currentContextDF, columnDelimiter);
        return [contextText, { [contextName.toLowerCase()]: currentContextDF }];
    }

    private arrayToCSV(data: Array<{ turn: string; content: string }>, delimiter: string): string {
        if (data.length === 0) return "";
        
        const headers = Object.keys(data[0]);
        const headerRow = headers.join(delimiter);
        const rows = data.map(row => 
            headers.map(header => row[header as keyof typeof row]).join(delimiter)
        );
        
        return [headerRow, ...rows].join("\n");
    }
}
