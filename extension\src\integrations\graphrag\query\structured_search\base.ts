﻿// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Base classes for search algos.
 */

import { ChatModel } from '../../language_model/protocol/base';
import {
  BasicContextBuilder,
  DRIFTContextBuilder,
  GlobalContextBuilder,
  LocalContextBuilder,
} from '../context_builder/builders';
import { ConversationHistory }
import { DataFrame }

/**
 * A Structured Search Result.
 */
export interface SearchResult {
  response: string | Record<string, any> | Record<string, any>[];
  contextData: string | DataFrame[] | Record<string, DataFrame>;
  // actual text strings that are in the context window, built from context_data
  contextText: string | string[] | Record<string, string>;
  completionTime: number;
  // total LLM calls and token usage
  llmCalls: number;
  promptTokens: number;
  outputTokens: number;
  // breakdown of LLM calls and token usage
  llmCallsCategories?: Record<string, number>;
  promptTokensCategories?: Record<string, number>;
  outputTokensCategories?: Record<string, number>;
}

/**
 * The Base Search implementation.
 */
export abstract class BaseSearch<T extends 
  | GlobalContextBuilder 
  | LocalContextBuilder 
  | DRIFTContextBuilder 
  | BasicContextBuilder
> {
  protected model: ChatModel;
  protected contextBuilder: T;
  protected tokenEncoder?: any;
  protected modelParams: Record<string, any>;
  protected contextBuilderParams: Record<string, any>;

  constructor(
    model: ChatModel,
    contextBuilder: T,
    tokenEncoder?: any,
    modelParams?: Record<string, any>,
    contextBuilderParams?: Record<string, any>
  ) {
    this.model = model;
    this.contextBuilder = contextBuilder;
    this.tokenEncoder = tokenEncoder;
    this.modelParams = modelParams || {};
    this.contextBuilderParams = contextBuilderParams || {};
  }

  /**
   * Search for the given query asynchronously.
   */
  abstract search(
    query: string,
    conversationHistory?: ConversationHistory,
    ...kwargs: any[]
  ): Promise<SearchResult>;

  /**
   * Stream search for the given query.
   */
  abstract streamSearch(
    query: string,
    conversationHistory?: ConversationHistory
  ): AsyncGenerator<string, void, unknown>;
}
