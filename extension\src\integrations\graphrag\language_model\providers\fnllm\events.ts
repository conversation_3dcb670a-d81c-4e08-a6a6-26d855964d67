﻿/**
 * Copyright (c) 2025 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * FNLLM llm events provider.
 */

import { ErrorHandlerFn } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;

export class FNLLMEvents {
    private _onError: ErrorHandlerFn;

    constructor(onError: ErrorHandlerFn) {
        this._onError = onError;
    }

    async onError(
        error?: Error,
        traceback?: string,
        arguments?: Record<string, any>
    ): Promise<void> {
        this._onError(error, traceback, arguments);
    }
}
