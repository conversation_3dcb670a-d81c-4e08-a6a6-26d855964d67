﻿/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Algorithm to dynamically select relevant communities with respect to a query.
 */

import { Community } from '../../data_model/community';
import { CommunityReport }
import { ChatModel } from '../../language_model/protocol/base';
import { RATE_QUERY }
import { rateRelevancy }

const logger = console;

export class DynamicCommunitySelection {
    private model: ChatModel;
    private tokenEncoder: any;
    private rateQuery: string;
    private numRepeats: number;
    private useSummary: boolean;
    private threshold: number;
    private keepParent: boolean;
    private maxLevel: number;
    private concurrentCoroutines: number;
    private modelParams: Record<string, any>;
    private reports: Record<string, CommunityReport>;
    private communities: Record<string, Community>;
    private levels: Record<string, string[]>;
    private startingCommunities: string[];

    constructor(options: {
        communityReports: CommunityReport[];
        communities: Community[];
        model: ChatModel;
        tokenEncoder: any;
        rateQuery?: string;
        useSummary?: boolean;
        threshold?: number;
        keepParent?: boolean;
        numRepeats?: number;
        maxLevel?: number;
        concurrentCoroutines?: number;
        modelParams?: Record<string, any>;
    }) {
        const {
            communityReports,
            communities,
            model,
            tokenEncoder,
            rateQuery = RATE_QUERY,
            useSummary = false,
            threshold = 1,
            keepParent = false,
            numRepeats = 1,
            maxLevel = 2,
            concurrentCoroutines = 8,
            modelParams = {}
        } = options;

        this.model = model;
        this.tokenEncoder = tokenEncoder;
        this.rateQuery = rateQuery;
        this.numRepeats = numRepeats;
        this.useSummary = useSummary;
        this.threshold = threshold;
        this.keepParent = keepParent;
        this.maxLevel = maxLevel;
        this.concurrentCoroutines = concurrentCoroutines;
        this.modelParams = modelParams;

        this.reports = {};
        for (const report of communityReports) {
            this.reports[report.communityId] = report;
        }

        this.communities = {};
        for (const community of communities) {
            this.communities[community.shortId] = community;
        }

        // Mapping from level to communities
        this.levels = {};
        for (const community of communities) {
            if (!this.levels[community.level]) {
                this.levels[community.level] = [];
            }
            if (community.shortId in this.reports) {
                this.levels[community.level].push(community.shortId);
            }
        }

        // Start from root communities (level 0)
        this.startingCommunities = this.levels["0"] || [];
    }

    async select(query: string): Promise<[CommunityReport[], Record<string, any>]> {
        const start = Date.now();
        let queue = [...this.startingCommunities];
        let level = 0;

        const ratings: Record<string, number> = {};
        const llmInfo: Record<string, any> = {
            llmCalls: 0,
            promptTokens: 0,
            outputTokens: 0,
        };
        const relevantCommunities = new Set<string>();

        while (queue.length > 0) {
            // Process communities in batches to respect concurrency limits
            const batchSize = Math.min(queue.length, this.concurrentCoroutines);
            const batch = queue.slice(0, batchSize);
            queue = queue.slice(batchSize);

            const gatherResults = await Promise.all(
                batch.map(community =>
                    rateRelevancy({
                        query,
                        description: this.useSummary
                            ? this.reports[community].summary
                            : this.reports[community].fullContent,
                        model: this.model,
                        tokenEncoder: this.tokenEncoder,
                        rateQuery: this.rateQuery,
                        numRepeats: this.numRepeats,
                        ...this.modelParams,
                    })
                )
            );

            const communitiesToRate: string[] = [];
            for (let i = 0; i < batch.length; i++) {
                const community = batch[i];
                const result = gatherResults[i];
                const rating = result.rating;

                logger.debug(
                    `dynamic community selection: community ${community} rating ${rating}`
                );

                ratings[community] = rating;
                llmInfo.llmCalls += result.llmCalls;
                llmInfo.promptTokens += result.promptTokens;
                llmInfo.outputTokens += result.outputTokens;

                if (rating >= this.threshold) {
                    relevantCommunities.add(community);
                    
                    // Find children nodes of the current node and append them to the queue
                    if (community in this.communities) {
                        for (const child of this.communities[community].children || []) {
                            if (child in this.reports) {
                                communitiesToRate.push(child);
                            } else {
                                logger.debug(
                                    `dynamic community selection: cannot find community ${child} in reports`
                                );
                            }
                        }
                    }

                    // Remove parent node if the current node is deemed relevant
                    if (!this.keepParent && community in this.communities) {
                        const parent = this.communities[community].parent;
                        if (parent) {
                            relevantCommunities.delete(parent);
                        }
                    }
                }
            }

            queue.push(...communitiesToRate);
            level += 1;

            if (
                queue.length === 0 &&
                relevantCommunities.size === 0 &&
                String(level) in this.levels &&
                level <= this.maxLevel
            ) {
                logger.debug(
                    `dynamic community selection: no relevant community reports, adding all reports at level ${level} to rate.`
                );
                // Append all communities at the next level to queue
                queue = [...this.levels[String(level)]];
            }
        }

        const communityReports = Array.from(relevantCommunities).map(
            community => this.reports[community]
        );
        const end = Date.now();

        // Count ratings distribution
        const ratingCounts: Record<number, number> = {};
        for (const rating of Object.values(ratings)) {
            ratingCounts[rating] = (ratingCounts[rating] || 0) + 1;
        }

        logger.debug(
            `dynamic community selection (took: ${Math.floor((end - start) / 1000)}s)\n` +
            `\trating distribution ${JSON.stringify(ratingCounts)}\n` +
            `\t${relevantCommunities.size} out of ${Object.keys(this.reports).length} community reports are relevant\n` +
            `\tprompt tokens: ${llmInfo.promptTokens}, output tokens: ${llmInfo.outputTokens}`
        );

        llmInfo.ratings = ratings;
        return [communityReports, llmInfo];
    }
}
