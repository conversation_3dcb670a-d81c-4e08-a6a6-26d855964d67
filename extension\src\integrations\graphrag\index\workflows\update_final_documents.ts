﻿/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing runWorkflow method definition.
 */

import { GraphRagConfig }
import { getUpdateStorages } from '../run/utils';
import { PipelineRunContext } from '../typing/context';
import { WorkflowFunctionOutput } from '../typing/workflow';
import { concatDataframes }

const logger = console;

/**
 * Update the documents from a incremental index run.
 */
export async function runWorkflow(
    config: GraphRagConfig,
    context: PipelineRunContext,
): Promise<WorkflowFunctionOutput> {
    logger.info("Workflow started: update_final_documents");
    
    const { outputStorage, previousStorage, deltaStorage } = getUpdateStorages(
        config, 
        context.state["update_timestamp"]
    );

    const finalDocuments = await concatDataframes(
        "documents", 
        previousStorage, 
        deltaStorage, 
        outputStorage
    );

    context.state["incremental_update_final_documents"] = finalDocuments;

    logger.info("Workflow completed: update_final_documents");
    return { result: null };
}
