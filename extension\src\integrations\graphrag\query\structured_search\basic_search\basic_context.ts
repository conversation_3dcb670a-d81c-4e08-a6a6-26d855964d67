﻿// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Basic Context Builder implementation.
 */

import { TextUnit } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { EmbeddingModel } from '../../../language_model/protocol/base';
import { BasicContextBuilder, ContextBuilderResult } from '../../context_builder/builders';
import { ConversationHistory } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { numTokens } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { BaseVectorStore } from '../../../vector_stores/base';
import { DataFrame } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;

/**
 * Class representing the Basic Search Context Builder.
 */
export class BasicSearchContext extends BasicContextBuilder {
  private textEmbedder: EmbeddingModel;
  private tokenEncoder?: any;
  private textUnits?: TextUnit[];
  private textUnitEmbeddings: BaseVectorStore;
  private embeddingVectorstoreKey: string;
  private textIdMap: Record<string, string>;

  constructor(
    textEmbedder: EmbeddingModel,
    textUnitEmbeddings: BaseVectorStore,
    textUnits?: TextUnit[],
    tokenEncoder?: any,
    embeddingVectorstoreKey: string = 'id'
  ) {
    super();
    this.textEmbedder = textEmbedder;
    this.tokenEncoder = tokenEncoder;
    this.textUnits = textUnits;
    this.textUnitEmbeddings = textUnitEmbeddings;
    this.embeddingVectorstoreKey = embeddingVectorstoreKey;
    this.textIdMap = this.mapIds();
  }

  buildContext(
    query: string,
    conversationHistory?: ConversationHistory,
    options: {
      k?: number;
      maxContextTokens?: number;
      contextName?: string;
      columnDelimiter?: string;
      textIdCol?: string;
      textCol?: string;
    } = {}
  ): ContextBuilderResult {
    const {
      k = 10,
      maxContextTokens = 12000,
      contextName = 'Sources',
      columnDelimiter = '|',
      textIdCol = 'source_id',
      textCol = 'text'
    } = options;

    /**
     * Build the context for the basic search mode.
     */
    let relatedTextDf: DataFrame;

    if (query !== '') {
      const relatedTexts = this.textUnitEmbeddings.similaritySearchByText(
        query,
        (text: string) => this.textEmbedder.embed(text),
        k
      );

      const relatedTextList = relatedTexts.map(chunk => ({
        [textIdCol]: this.textIdMap[chunk.document.id],
        [textCol]: chunk.document.text
      }));

      // Convert to DataFrame format
      relatedTextDf = {
        length: relatedTextList.length,
        [textIdCol]: relatedTextList.map(item => item[textIdCol]),
        [textCol]: relatedTextList.map(item => item[textCol])
      };
    } else {
      relatedTextDf = {
        length: 0,
        [textIdCol]: [],
        [textCol]: []
      };
    }

    // Add these related text chunks into context until we fill up the context window
    let currentTokens = 0;
    const textIds: number[] = [];
    
    currentTokens = numTokens(
      `${textIdCol}${columnDelimiter}${textCol}\n`,
      this.tokenEncoder
    );

    for (let i = 0; i < relatedTextDf.length; i++) {
      const text = `${relatedTextDf[textIdCol][i]}${columnDelimiter}${relatedTextDf[textCol][i]}\n`;
      const tokens = numTokens(text, this.tokenEncoder);
      
      if (currentTokens + tokens > maxContextTokens) {
        console.warn(
          `Reached token limit: ${currentTokens + tokens}. Reverting to previous context state`
        );
        break;
      }

      currentTokens += tokens;
      textIds.push(i);
    }

    // Create final DataFrame with selected indices
    const finalTextDf: DataFrame = {
      length: textIds.length,
      [textIdCol]: textIds.map(i => relatedTextDf[textIdCol][i]),
      [textCol]: textIds.map(i => relatedTextDf[textCol][i])
    };

    // Convert to CSV format
    const finalText = this.dataFrameToCsv(finalTextDf, columnDelimiter);

    return {
      contextChunks: finalText,
      contextRecords: { [contextName]: finalTextDf },
      llmCalls: 0,
      promptTokens: 0,
      outputTokens: 0
    };
  }

  /**
   * Map id to short id in the text units.
   */
  private mapIds(): Record<string, string> {
    const idMap: Record<string, string> = {};
    const textUnits = this.textUnits || [];
    
    for (const unit of textUnits) {
      idMap[unit.id] = unit.shortId;
    }
    
    return idMap;
  }

  /**
   * Convert DataFrame to CSV format.
   */
  private dataFrameToCsv(df: DataFrame, delimiter: string): string {
    if (df.length === 0) {
      return '';
    }

    const columns = Object.keys(df).filter(key => key !== 'length');
    const header = columns.join(delimiter);
    
    const rows: string[] = [header];
    
    for (let i = 0; i < df.length; i++) {
      const row = columns.map(col => {
        const value = df[col][i];
        // Escape delimiter and quotes
        const escaped = String(value).replace(/\\/g, '\\\\').replace(new RegExp(delimiter, 'g'), `\\${delimiter}`);
        return escaped;
      }).join(delimiter);
      rows.push(row);
    }
    
    return rows.join('\n');
  }
}
