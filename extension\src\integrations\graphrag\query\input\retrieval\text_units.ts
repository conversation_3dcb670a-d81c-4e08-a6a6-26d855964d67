﻿// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Util functions to retrieve text units from a collection.
 */

import { Entity } from '../../../data_model/entity';
import { TextUnit } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { DataFrame } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;

/**
 * Get all text units that are associated to selected entities.
 */
export function getCandidateTextUnits(
  selectedEntities: Entity[],
  textUnits: TextUnit[]
): DataFrame {
  const selectedTextIds = selectedEntities
    .filter(entity => entity.textUnitIds)
    .flatMap(entity => entity.textUnitIds!);

  const selectedTextUnits = textUnits.filter(unit =>
    selectedTextIds.includes(unit.id)
  );

  return toTextUnitDataframe(selectedTextUnits);
}

/**
 * Convert a list of text units to a pandas dataframe.
 */
export function toTextUnitDataframe(textUnits: TextUnit[]): DataFrame {
  if (textUnits.length === 0) {
    return { length: 0 };
  }

  // Add header
  const header = ['id', 'text'];
  const attributeCols = textUnits[0].attributes ? Object.keys(textUnits[0].attributes) : [];
  const filteredAttributeCols = attributeCols.filter(col => !header.includes(col));
  header.push(...filteredAttributeCols);

  const dataFrame: DataFrame = { length: textUnits.length };
  
  // Initialize columns
  header.forEach(col => {
    dataFrame[col] = [];
  });

  // Fill data
  textUnits.forEach(unit => {
    dataFrame.id.push(unit.shortId);
    dataFrame.text.push(unit.text);
    
    // Add attribute columns
    filteredAttributeCols.forEach(field => {
      const value = unit.attributes?.[field];
      dataFrame[field].push(value ? String(value) : '');
    });
  });

  return dataFrame;
}
