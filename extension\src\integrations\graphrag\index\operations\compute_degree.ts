/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing compute_degree definition.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import * as nx from 'networkx';
import { DataFrame } from '../../data_model/types.js';

/**
 * Create a new DataFrame with the degree of each node in the graph.
 * Matches the Python compute_degree function exactly.
 */
export function compute_degree(graph: nx.Graph): DataFrame {
    // Python: return pd.DataFrame([
    //     {"title": node, "degree": int(degree)}
    //     for node, degree in graph.degree  # type: ignore
    // ])
    const data = [];

    // Get degree for each node
    for (const [node, degree] of graph.degree()) {
        data.push({
            title: node,
            degree: parseInt(String(degree), 10)
        });
    }

    return {
        columns: ['title', 'degree'],
        data
    };
}

// Compatibility export for existing code
export const computeDegree = compute_degree;