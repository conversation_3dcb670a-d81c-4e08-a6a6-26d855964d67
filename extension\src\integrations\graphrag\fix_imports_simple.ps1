# PowerShell script to replace hyphens with underscores in import statements
# Only processes import statements, leaves other code unchanged

Write-Host "Starting to replace hyphens with underscores in import statements..." -ForegroundColor Green

$totalFiles = 0
$changedFiles = 0

# Get all TypeScript and JavaScript files
$files = Get-ChildItem -Path . -Recurse -Include "*.ts", "*.js", "*.tsx", "*.jsx"

foreach ($file in $files) {
    $totalFiles++
    $lines = Get-Content $file.FullName -Encoding UTF8
    $changed = $false
    $newLines = @()
    
    foreach ($line in $lines) {
        $originalLine = $line
        
        # Check if this is an import statement with hyphens
        if ($line -match "import.*from.*[`"'].*-.*[`"']") {
            # Replace hyphens with underscores in the path part after 'from'
            $newLine = $line -replace "(from\s*[`"'])([^`"']*?)([`"'])", {
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            }
            
            if ($newLine -ne $originalLine) {
                $changed = $true
                Write-Host "  File: $($file.Name)" -ForegroundColor Gray
                Write-Host "  Old:  $originalLine" -ForegroundColor Red
                Write-Host "  New:  $newLine" -ForegroundColor Green
            }
            $newLines += $newLine
        } else {
            $newLines += $line
        }
    }
    
    # Write back to file if changed
    if ($changed) {
        $newLines | Set-Content -Path $file.FullName -Encoding UTF8
        $changedFiles++
        Write-Host "Processed: $($file.FullName)" -ForegroundColor Yellow
    }
    
    # Show progress every 50 files
    if ($totalFiles % 50 -eq 0) {
        Write-Host "Processed $totalFiles files, changed $changedFiles files" -ForegroundColor Cyan
    }
}

Write-Host ""
Write-Host "Processing complete!" -ForegroundColor Green
Write-Host "Total files processed: $totalFiles" -ForegroundColor White
Write-Host "Files changed: $changedFiles" -ForegroundColor White
