# PowerShell script to fix broken import statements
# Removes the PowerShell code that was accidentally inserted into import statements

Write-Host "Starting to fix broken import statements..." -ForegroundColor Green

$totalFiles = 0
$fixedFiles = 0

# Get all TypeScript and JavaScript files
$files = Get-ChildItem -Path . -Recurse -Include "*.ts", "*.js", "*.tsx", "*.jsx"

foreach ($file in $files) {
    $totalFiles++
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    $originalContent = $content
    
    # Pattern to match broken import statements
    # Look for import statements that have PowerShell code inserted
    $brokenPattern = @"
import \{ ([^}]+) \} 
                param\(\$match\)
                \$prefix = \$match\.Groups\[1\]\.Value
                \$path = \$match\.Groups\[2\]\.Value  
                \$suffix = \$match\.Groups\[3\]\.Value
                \$newPath = \$path -replace '-', '_'
                return \$prefix \+ \$newPath \+ \$suffix
            ;
"@
    
    # Also handle cases where the import is incomplete
    $incompletePattern = @"
import \{ ([^}]+) \}
                param\(\$match\)
                \$prefix = \$match\.Groups\[1\]\.Value
                \$path = \$match\.Groups\[2\]\.Value  
                \$suffix = \$match\.Groups\[3\]\.Value
                \$newPath = \$path -replace '-', '_'
                return \$prefix \+ \$newPath \+ \$suffix
            ;
"@
    
    $changed = $false
    
    # Fix the broken imports by removing the PowerShell code
    if ($content -match "param\(\`$match\)") {
        Write-Host "Fixing file: $($file.FullName)" -ForegroundColor Yellow
        
        # Remove all the PowerShell code blocks
        $content = $content -replace "(?s)\s*param\(\`$match\).*?return \`$prefix \+ \`$newPath \+ \`$suffix\s*;", ""
        
        # Fix incomplete import statements - we need to reconstruct them
        # This is a more complex fix that requires manual intervention for each case
        $lines = $content -split "`n"
        $fixedLines = @()
        $i = 0
        
        while ($i -lt $lines.Length) {
            $line = $lines[$i]
            
            # Check if this line starts an incomplete import
            if ($line -match "^import \{ ([^}]+) \}$" -and $i + 1 -lt $lines.Length) {
                $importItems = $matches[1]
                
                # Look ahead to see if the next lines contain PowerShell code
                $j = $i + 1
                while ($j -lt $lines.Length -and $lines[$j] -match "^\s*(param|\$|return)") {
                    $j++
                }
                
                # If we found PowerShell code, skip it and try to reconstruct the import
                if ($j -gt $i + 1) {
                    # This is a broken import, we need to guess the 'from' part
                    # For now, just add a placeholder comment
                    $fixedLines += "// FIXME: Broken import needs manual fix: import { $importItems } from '???';"
                    $i = $j
                    $changed = $true
                    continue
                }
            }
            
            $fixedLines += $line
            $i++
        }
        
        $content = $fixedLines -join "`n"
        $changed = $true
    }
    
    # Write back to file if changed
    if ($changed) {
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8 -NoNewline
        $fixedFiles++
        Write-Host "Fixed: $($file.FullName)" -ForegroundColor Green
    }
    
    # Show progress every 50 files
    if ($totalFiles % 50 -eq 0) {
        Write-Host "Processed $totalFiles files, fixed $fixedFiles files" -ForegroundColor Cyan
    }
}

Write-Host ""
Write-Host "Fix complete!" -ForegroundColor Green
Write-Host "Total files processed: $totalFiles" -ForegroundColor White
Write-Host "Files fixed: $fixedFiles" -ForegroundColor White
Write-Host ""
Write-Host "Note: Some imports may need manual fixing where marked with FIXME comments" -ForegroundColor Yellow
