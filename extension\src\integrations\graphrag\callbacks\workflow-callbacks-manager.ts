// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * A module containing the WorkflowCallbacks registry.
 */

import { WorkflowCallbacks } from './workflow-callbacks';
import { PipelineRunResult } from '../index/typing/pipeline-run-result';
import { Progress } from '../logger/progress';

/**
 * A registry of WorkflowCallbacks.
 */
export class WorkflowCallbacksManager implements WorkflowCallbacks {
    private callbacks: WorkflowCallbacks[] = [];

    /**
     * Create a new instance of WorkflowCallbacksManager.
     */
    constructor() {
        this.callbacks = [];
    }

    /**
     * Register a new WorkflowCallbacks type.
     */
    register(callbacks: WorkflowCallbacks): void {
        this.callbacks.push(callbacks);
    }

    /**
     * Execute this callback when the entire pipeline starts.
     */
    pipelineStart(names: string[]): void {
        for (const callback of this.callbacks) {
            if ('pipelineStart' in callback && typeof callback.pipelineStart === 'function') {
                callback.pipelineStart(names);
            }
        }
    }

    /**
     * Execute this callback when the entire pipeline ends.
     */
    pipelineEnd(results: PipelineRunResult[]): void {
        for (const callback of this.callbacks) {
            if ('pipelineEnd' in callback && typeof callback.pipelineEnd === 'function') {
                callback.pipelineEnd(results);
            }
        }
    }

    /**
     * Execute this callback when a workflow starts.
     */
    workflowStart(name: string, instance: object): void {
        for (const callback of this.callbacks) {
            if ('workflowStart' in callback && typeof callback.workflowStart === 'function') {
                callback.workflowStart(name, instance);
            }
        }
    }

    /**
     * Execute this callback when a workflow ends.
     */
    workflowEnd(name: string, instance: object): void {
        for (const callback of this.callbacks) {
            if ('workflowEnd' in callback && typeof callback.workflowEnd === 'function') {
                callback.workflowEnd(name, instance);
            }
        }
    }

    /**
     * Handle when progress occurs.
     */
    progress(progress: Progress): void {
        for (const callback of this.callbacks) {
            if ('progress' in callback && typeof callback.progress === 'function') {
                callback.progress(progress);
            }
        }
    }
}