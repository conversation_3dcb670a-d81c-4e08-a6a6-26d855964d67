﻿// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Contains algorithms to build context data for global search prompt.
 */

import { Community } from '../../../data_model/community';
import { CommunityReport }
import { Entity } from '../../../data_model/entity';
import { Context<PERSON><PERSON>erR<PERSON>ult, GlobalContextBuilder } from '../../context_builder/builders';
import { buildCommunityContext }
import { ConversationHistory }
import { DynamicCommunitySelection }

/**
 * GlobalSearch community context builder.
 */
export class GlobalCommunityContext extends GlobalContextBuilder {
  private communityReports: CommunityReport[];
  private entities?: Entity[];
  private tokenEncoder?: any;
  private dynamicCommunitySelection?: DynamicCommunitySelection;
  private randomState: number;

  constructor(
    communityReports: CommunityReport[],
    communities: Community[],
    entities?: Entity[],
    tokenEncoder?: any,
    dynamicCommunitySelection: boolean = false,
    dynamicCommunitySelectionKwargs?: Record<string, any>,
    randomState: number = 86
  ) {
    super();
    this.communityReports = communityReports;
    this.entities = entities;
    this.tokenEncoder = tokenEncoder;
    this.randomState = randomState;

    if (dynamicCommunitySelection && dynamicCommunitySelectionKwargs) {
      this.dynamicCommunitySelection = new DynamicCommunitySelection(
        communityReports,
        communities,
        dynamicCommunitySelectionKwargs.model,
        dynamicCommunitySelectionKwargs.tokenEncoder,
        dynamicCommunitySelectionKwargs
      );
    }
  }

  async buildContext(
    query: string,
    conversationHistory?: ConversationHistory,
    options: {
      useCommunitySummary?: boolean;
      columnDelimiter?: string;
      shuffleData?: boolean;
      includeCommunityRank?: boolean;
      minCommunityRank?: number;
      communityRankName?: string;
      includeCommunityWeight?: boolean;
      communityWeightName?: string;
      normalizeCommunityWeight?: boolean;
      maxContextTokens?: number;
      contextName?: string;
      conversationHistoryUserTurnsOnly?: boolean;
      conversationHistoryMaxTurns?: number;
    } = {}
  ): Promise<ContextBuilderResult> {
    const {
      useCommunitySummary = true,
      columnDelimiter = '|',
      shuffleData = true,
      includeCommunityRank = false,
      minCommunityRank = 0,
      communityRankName = 'rank',
      includeCommunityWeight = true,
      communityWeightName = 'occurrence',
      normalizeCommunityWeight = true,
      maxContextTokens = 8000,
      contextName = 'Reports',
      conversationHistoryUserTurnsOnly = true,
      conversationHistoryMaxTurns = 5
    } = options;

    /**
     * Prepare batches of community report data table as context data for global search.
     */
    let conversationHistoryContext = '';
    let finalContextData: Record<string, any> = {};
    let llmCalls = 0;
    let promptTokens = 0;
    let outputTokens = 0;

    if (conversationHistory) {
      // build conversation history context
      const historyResult = conversationHistory.buildContext({
        includeUserTurnsOnly: conversationHistoryUserTurnsOnly,
        maxQaTurns: conversationHistoryMaxTurns,
        columnDelimiter,
        maxContextTokens,
        recencyBias: false
      });

      conversationHistoryContext = historyResult.contextText;
      if (conversationHistoryContext !== '') {
        finalContextData = historyResult.contextData;
      }
    }

    let communityReports = this.communityReports;
    if (this.dynamicCommunitySelection) {
      const selectionResult = await this.dynamicCommunitySelection.select(query);
      communityReports = selectionResult.communityReports;
      llmCalls += selectionResult.llmCalls;
      promptTokens += selectionResult.promptTokens;
      outputTokens += selectionResult.outputTokens;
    }

    const communityContextResult = buildCommunityContext(
      communityReports,
      this.entities,
      {
        tokenEncoder: this.tokenEncoder,
        useCommunitySummary,
        columnDelimiter,
        shuffleData,
        includeCommunityRank,
        minCommunityRank,
        communityRankName,
        includeCommunityWeight,
        communityWeightName,
        normalizeCommunityWeight,
        maxContextTokens,
        singleBatch: false,
        contextName,
        randomState: this.randomState
      }
    );

    // Prepare context_prefix based on whether conversation_history_context exists
    const contextPrefix = conversationHistoryContext 
      ? `${conversationHistoryContext}\n\n`
      : '';

    const finalContext = Array.isArray(communityContextResult.contextChunks)
      ? communityContextResult.contextChunks.map(context => `${contextPrefix}${context}`)
      : `${contextPrefix}${communityContextResult.contextChunks}`;

    // Update the final context data with the provided community_context_data
    Object.assign(finalContextData, communityContextResult.contextRecords);

    return {
      contextChunks: finalContext,
      contextRecords: finalContextData,
      llmCalls,
      promptTokens,
      outputTokens
    };
  }
}
