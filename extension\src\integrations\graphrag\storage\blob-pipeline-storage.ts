/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 * 
 * Azure Blob Storage implementation of PipelineStorage.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import * as path from 'path'
import { PipelineStorage, getTimestampFormattedWithLocalTz } from './pipeline-storage'

// Azure SDK types (would be imported from @azure/storage-blob in real implementation)
interface BlobServiceClient {
    fromConnectionString(connectionString: string): BlobServiceClient
    getContainerClient(containerName: string): ContainerClient
    listContainers(): AsyncIterableIterator<ContainerItem>
    createContainer(containerName: string): Promise<void>
    deleteContainer(containerName: string): Promise<void>
}

interface ContainerClient {
    listBlobs(): AsyncIterableIterator<BlobItem>
    getBlobClient(blobName: string): BlobClient
}

interface BlobClient {
    downloadBlob(): Promise<BlobDownloadResponse>
    uploadBlob(data: Buffer | string, options?: { overwrite?: boolean }): Promise<void>
    exists(): Promise<boolean>
    deleteBlob(): Promise<void>
}

interface BlobItem {
    name: string
}

interface ContainerItem {
    name: string
}

interface BlobDownloadResponse {
    readableStreamBody?: NodeJS.ReadableStream
    properties: {
        creationTime: Date
    }
}

interface DefaultAzureCredential {
    // Placeholder for Azure credential
}

/**
 * The Blob-Storage implementation.
 */
export class BlobPipelineStorage extends PipelineStorage {
    private _connectionString?: string
    private _containerName: string
    private _pathPrefix: string
    private _encoding: string
    private _storageAccountBlobUrl?: string
    private _storageAccountName?: string
    private _blobServiceClient: any // Would be BlobServiceClient in real implementation

    constructor(
        connectionString?: string,
        containerName: string = "default",
        encoding: string = "utf-8",
        pathPrefix?: string,
        storageAccountBlobUrl?: string
    ) {
        super()
        
        if (connectionString) {
            // In real implementation: this._blobServiceClient = BlobServiceClient.fromConnectionString(connectionString)
            this._blobServiceClient = this._createMockBlobServiceClient()
        } else {
            if (!storageAccountBlobUrl) {
                throw new Error("Either connection_string or storage_account_blob_url must be provided.")
            }
            
            // In real implementation: 
            // this._blobServiceClient = new BlobServiceClient(storageAccountBlobUrl, new DefaultAzureCredential())
            this._blobServiceClient = this._createMockBlobServiceClient()
        }

        this._encoding = encoding
        this._containerName = containerName
        this._connectionString = connectionString
        this._pathPrefix = pathPrefix || ""
        this._storageAccountBlobUrl = storageAccountBlobUrl
        this._storageAccountName = storageAccountBlobUrl 
            ? storageAccountBlobUrl.split("//")[1].split(".")[0]
            : undefined

        console.log(
            `Creating blob storage at container=${this._containerName}, path=${this._pathPrefix}`
        )
        
        this._createContainer()
    }

    private _createMockBlobServiceClient(): any {
        // Mock implementation for development/testing
        return {
            listContainers: async function* () {
                yield { name: "default" }
            },
            createContainer: async (name: string) => {
                console.log(`Mock: Creating container ${name}`)
            },
            deleteContainer: async (name: string) => {
                console.log(`Mock: Deleting container ${name}`)
            },
            getContainerClient: (name: string) => ({
                listBlobs: async function* () {
                    // Mock empty blob list
                },
                getBlobClient: (blobName: string) => ({
                    downloadBlob: async () => ({
                        readableStreamBody: null,
                        properties: { creationTime: new Date() }
                    }),
                    uploadBlob: async (data: any, options?: any) => {
                        console.log(`Mock: Uploading blob ${blobName}`)
                    },
                    exists: async () => false,
                    deleteBlob: async () => {
                        console.log(`Mock: Deleting blob ${blobName}`)
                    }
                })
            })
        }
    }

    private async _createContainer(): Promise<void> {
        if (!(await this._containerExists())) {
            const containerNames: string[] = []
            for await (const container of this._blobServiceClient.listContainers()) {
                containerNames.push(container.name)
            }
            
            if (!containerNames.includes(this._containerName)) {
                await this._blobServiceClient.createContainer(this._containerName)
            }
        }
    }

    private async _deleteContainer(): Promise<void> {
        if (await this._containerExists()) {
            await this._blobServiceClient.deleteContainer(this._containerName)
        }
    }

    private async _containerExists(): Promise<boolean> {
        const containerNames: string[] = []
        for await (const container of this._blobServiceClient.listContainers()) {
            containerNames.push(container.name)
        }
        return containerNames.includes(this._containerName)
    }

    async* find(
        filePattern: RegExp,
        baseDir?: string,
        fileFilter?: Record<string, any>,
        maxCount: number = -1
    ): AsyncIterableIterator<[string, Record<string, any>]> {
        baseDir = baseDir || ""

        console.log(
            `Searching container ${this._containerName} for files matching ${filePattern.source}`
        )

        const blobName = (blobName: string): string => {
            if (blobName.startsWith(this._pathPrefix)) {
                blobName = blobName.replace(this._pathPrefix, "")
            }
            if (blobName.startsWith("/")) {
                blobName = blobName.substring(1)
            }
            return blobName
        }

        const itemFilter = (item: Record<string, any>): boolean => {
            if (!fileFilter) {return true}
            return Object.entries(fileFilter).every(([key, value]) => {
                const regex = new RegExp(value)
                return regex.test(item[key])
            })
        }

        try {
            const containerClient = this._blobServiceClient.getContainerClient(this._containerName)
            const allBlobs: BlobItem[] = []
            
            for await (const blob of containerClient.listBlobs()) {
                allBlobs.push(blob)
            }

            let numLoaded = 0
            const numTotal = allBlobs.length
            let numFiltered = 0

            for (const blob of allBlobs) {
                const match = filePattern.exec(blob.name)
                if (match && match.groups && blob.name.startsWith(baseDir)) {
                    const group = match.groups
                    if (itemFilter(group)) {
                        yield [blobName(blob.name), group]
                        numLoaded++
                        if (maxCount > 0 && numLoaded >= maxCount) {
                            break
                        }
                    } else {
                        numFiltered++
                    }
                } else {
                    numFiltered++
                }
                
                console.debug(
                    `Blobs loaded: ${numLoaded}, filtered: ${numFiltered}, total: ${numTotal}`
                )
            }
        } catch (error) {
            console.error(
                `Error finding blobs: base_dir=${baseDir}, file_pattern=${filePattern.source}, file_filter=${JSON.stringify(fileFilter)}`,
                error
            )
            throw error
        }
    }

    async get(key: string, asBytes: boolean = false, encoding?: string): Promise<any> {
        try {
            key = this._keyname(key)
            const containerClient = this._blobServiceClient.getContainerClient(this._containerName)
            const blobClient = containerClient.getBlobClient(key)
            const response = await blobClient.downloadBlob()
            
            // In real implementation, you would read from response.readableStreamBody
            // For now, return mock data
            const blobData = Buffer.from("mock blob data")
            
            if (!asBytes) {
                const coding = encoding || this._encoding
                return blobData.toString(coding)
            }
            return blobData
        } catch (error) {
            console.error(`Error getting key ${key}`, error)
            return null
        }
    }

    async set(key: string, value: any, encoding?: string): Promise<void> {
        try {
            key = this._keyname(key)
            const containerClient = this._blobServiceClient.getContainerClient(this._containerName)
            const blobClient = containerClient.getBlobClient(key)
            
            if (Buffer.isBuffer(value)) {
                await blobClient.uploadBlob(value, { overwrite: true })
            } else {
                const coding = encoding || this._encoding
                const buffer = Buffer.from(value, coding)
                await blobClient.uploadBlob(buffer, { overwrite: true })
            }
        } catch (error) {
            console.error(`Error setting key ${key}`, error)
            throw error
        }
    }

    private _setDfJson(key: string, dataframe: any): void {
        // DataFrame operations would require additional libraries
        // This is a placeholder for pandas-like operations
        console.warn("DataFrame JSON operations not implemented in TypeScript version")
    }

    private _setDfParquet(key: string, dataframe: any): void {
        // DataFrame operations would require additional libraries
        // This is a placeholder for pandas-like operations
        console.warn("DataFrame Parquet operations not implemented in TypeScript version")
    }

    async has(key: string): Promise<boolean> {
        key = this._keyname(key)
        const containerClient = this._blobServiceClient.getContainerClient(this._containerName)
        const blobClient = containerClient.getBlobClient(key)
        return await blobClient.exists()
    }

    async delete(key: string): Promise<void> {
        key = this._keyname(key)
        const containerClient = this._blobServiceClient.getContainerClient(this._containerName)
        const blobClient = containerClient.getBlobClient(key)
        await blobClient.deleteBlob()
    }

    async clear(): Promise<void> {
        // Clear implementation would delete all blobs in the container
        console.warn("BlobPipelineStorage.clear() - Implementation depends on specific requirements")
    }

    child(name?: string): PipelineStorage {
        if (!name) {
            return this
        }
        const newPath = path.join(this._pathPrefix, name)
        return new BlobPipelineStorage(
            this._connectionString,
            this._containerName,
            this._encoding,
            newPath,
            this._storageAccountBlobUrl
        )
    }

    keys(): string[] {
        throw new Error("Blob storage does not yet support listing keys.")
    }

    private _keyname(key: string): string {
        return path.join(this._pathPrefix, key)
    }

    private _abfsUrl(key: string): string {
        const fullPath = path.join(this._containerName, this._pathPrefix, key)
        return `abfs://${fullPath}`
    }

    async getCreationDate(key: string): Promise<string> {
        try {
            key = this._keyname(key)
            const containerClient = this._blobServiceClient.getContainerClient(this._containerName)
            const blobClient = containerClient.getBlobClient(key)
            const response = await blobClient.downloadBlob()
            const timestamp = response.properties.creationTime
            return getTimestampFormattedWithLocalTz(timestamp)
        } catch (error) {
            console.error(`Error getting creation date for key ${key}`, error)
            return ""
        }
    }
}

/**
 * Create a blob based storage.
 */
export function createBlobStorage(kwargs: Record<string, any>): PipelineStorage {
    const connectionString = kwargs.connection_string
    const storageAccountBlobUrl = kwargs.storage_account_blob_url
    const baseDir = kwargs.base_dir
    const containerName = kwargs.container_name
    
    console.log(`Creating blob storage at ${containerName}`)
    
    if (!containerName) {
        throw new Error("No container name provided for blob storage.")
    }
    
    if (!connectionString && !storageAccountBlobUrl) {
        throw new Error("No storage account blob url provided for blob storage.")
    }
    
    return new BlobPipelineStorage(
        connectionString,
        containerName,
        "utf-8",
        baseDir,
        storageAccountBlobUrl
    )
}

/**
 * Check if the provided blob container name is valid based on Azure rules.
 */
export function validateBlobContainerName(containerName: string): boolean {
    // Check the length of the name
    if (containerName.length < 3 || containerName.length > 63) {
        throw new Error(
            `Container name must be between 3 and 63 characters in length. Name provided was ${containerName.length} characters long.`
        )
    }

    // Check if the name starts with a letter or number
    if (!/^[a-zA-Z0-9]/.test(containerName)) {
        throw new Error(
            `Container name must start with a letter or number. Starting character was ${containerName[0]}.`
        )
    }

    // Check for valid characters (letters, numbers, hyphen) and lowercase letters
    if (!/^[a-z0-9-]+$/.test(containerName)) {
        throw new Error(
            `Container name must only contain:\n- lowercase letters\n- numbers\n- or hyphens\nName provided was ${containerName}.`
        )
    }

    // Check for consecutive hyphens
    if (containerName.includes("--")) {
        throw new Error(
            `Container name cannot contain consecutive hyphens. Name provided was ${containerName}.`
        )
    }

    // Check for hyphens at the end of the name
    if (containerName.endsWith("-")) {
        throw new Error(
            `Container name cannot end with a hyphen. Name provided was ${containerName}.`
        )
    }

    return true
}