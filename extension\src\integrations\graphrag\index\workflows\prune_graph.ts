﻿/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing runWorkflow method definition.
 */

import { GraphRagConfig } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { PruneGraphConfig } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { createGraph } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { graphToDataframes } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { pruneGraph as pruneGraphOperation } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { PipelineRunContext } from '../typing/context';
import { WorkflowFunctionOutput } from '../typing/workflow';
import { loadTableFromStorage, writeTableToStorage } from '../../utils/storage';

const logger = console;

export interface DataFrame {
    [key: string]: any[];
    length: number;
}

/**
 * All the steps to create the base entity graph.
 */
export async function runWorkflow(
    config: GraphRagConfig,
    context: PipelineRunContext,
): Promise<WorkflowFunctionOutput> {
    logger.info("Workflow started: prune_graph");
    
    const entities = await loadTableFromStorage("entities", context.outputStorage);
    const relationships = await loadTableFromStorage("relationships", context.outputStorage);

    const { prunedEntities, prunedRelationships } = pruneGraph({
        entities,
        relationships,
        pruningConfig: config.pruneGraph,
    });

    await writeTableToStorage(prunedEntities, "entities", context.outputStorage);
    await writeTableToStorage(prunedRelationships, "relationships", context.outputStorage);

    logger.info("Workflow completed: prune_graph");
    return {
        result: {
            entities: prunedEntities,
            relationships: prunedRelationships,
        }
    };
}

export interface PruneGraphParams {
    entities: DataFrame;
    relationships: DataFrame;
    pruningConfig: PruneGraphConfig;
}

export interface PruneGraphResult {
    prunedEntities: DataFrame;
    prunedRelationships: DataFrame;
}

/**
 * Prune a full graph based on graph statistics.
 */
export function pruneGraph({
    entities,
    relationships,
    pruningConfig,
}: PruneGraphParams): PruneGraphResult {
    // Create a temporary graph to prune, then turn it back into dataframes
    const graph = createGraph(relationships, ["weight"], entities);
    
    const pruned = pruneGraphOperation({
        graph,
        minNodeFreq: pruningConfig.minNodeFreq,
        maxNodeFreqStd: pruningConfig.maxNodeFreqStd,
        minNodeDegree: pruningConfig.minNodeDegree,
        maxNodeDegreeStd: pruningConfig.maxNodeDegreeStd,
        minEdgeWeightPct: pruningConfig.minEdgeWeightPct,
        removeEgoNodes: pruningConfig.removeEgoNodes,
        lccOnly: pruningConfig.lccOnly,
    });

    const { prunedNodes, prunedEdges } = graphToDataframes({
        graph: pruned,
        nodeColumns: ["title"],
        edgeColumns: ["source", "target"],
    });

    // Subset the full nodes and edges to only include the pruned remainders
    const subsetEntities = mergeDataFrames(prunedNodes, entities, "title", "title", "inner");
    const subsetRelationships = mergeDataFrames(
        prunedEdges, 
        relationships, 
        ["source", "target"], 
        ["source", "target"], 
        "inner"
    );

    return {
        prunedEntities: subsetEntities,
        prunedRelationships: subsetRelationships,
    };
}

/**
 * Merge two DataFrames based on specified columns.
 */
function mergeDataFrames(
    left: DataFrame,
    right: DataFrame,
    leftOn: string | string[],
    rightOn: string | string[],
    how: "inner" | "left" | "right" | "outer"
): DataFrame {
    const result: DataFrame = { length: 0 };
    const mergedRows: any[] = [];

    const leftKeys = Array.isArray(leftOn) ? leftOn : [leftOn];
    const rightKeys = Array.isArray(rightOn) ? rightOn : [rightOn];

    // Get all column names from both DataFrames
    const leftColumns = Object.keys(left).filter(key => key !== 'length');
    const rightColumns = Object.keys(right).filter(key => key !== 'length');
    const allColumns = [...new Set([...leftColumns, ...rightColumns])];

    // Create key comparison function
    const createKey = (df: DataFrame, keys: string[], index: number): string => {
        return keys.map(key => String(df[key]?.[index] || '')).join('|');
    };

    if (how === "inner") {
        for (let i = 0; i < left.length; i++) {
            const leftKey = createKey(left, leftKeys, i);
            
            for (let j = 0; j < right.length; j++) {
                const rightKey = createKey(right, rightKeys, j);
                
                if (leftKey === rightKey) {
                    const mergedRow: any = {};
                    
                    // Add left columns
                    for (const col of leftColumns) {
                        if (col !== 'length') {
                            mergedRow[col] = left[col][i];
                        }
                    }
                    
                    // Add right columns (avoid duplicating join keys)
                    for (const col of rightColumns) {
                        if (col !== 'length' && !rightKeys.includes(col)) {
                            mergedRow[col] = right[col][j];
                        }
                    }
                    
                    mergedRows.push(mergedRow);
                }
            }
        }
    }

    // Convert merged rows back to DataFrame format
    result.length = mergedRows.length;
    for (const col of allColumns) {
        if (col !== 'length') {
            result[col] = mergedRows.map(row => row[col] || null);
        }
    }

    return result;
}
