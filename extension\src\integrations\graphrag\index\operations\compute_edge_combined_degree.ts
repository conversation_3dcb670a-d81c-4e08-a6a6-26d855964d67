/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing compute_edge_combined_degree methods definition.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { DataFrame } from '../../data_model/types.js';

/**
 * Compute the combined degree for each edge in a graph.
 * Matches the Python compute_edge_combined_degree function exactly.
 */
export function compute_edge_combined_degree(
    edge_df: DataFrame,
    node_degree_df: DataFrame,
    node_name_column: string,
    node_degree_column: string,
    edge_source_column: string,
    edge_target_column: string
): number[] {
    // Python: def join_to_degree(df: pd.DataFrame, column: str) -> pd.DataFrame:
    function join_to_degree(df: DataFrame, column: string): DataFrame {
        // Python: degree_column = _degree_colname(column)
        const degree_column = _degree_colname(column);

        // Python: result = df.merge(
        //     node_degree_df.rename(
        //         columns={node_name_column: column, node_degree_column: degree_column}
        //     ),
        //     on=column,
        //     how="left",
        // )
        const renamed_node_df = {
            columns: [column, degree_column],
            data: node_degree_df.data.map(row => ({
                [column]: row[node_name_column],
                [degree_column]: row[node_degree_column]
            }))
        };

        // Perform left join
        const result_data = df.data.map(row => {
            const match = renamed_node_df.data.find(node_row => node_row[column] === row[column]);
            return {
                ...row,
                [degree_column]: match ? match[degree_column] : null
            };
        });

        const result: DataFrame = {
            columns: [...df.columns, degree_column],
            data: result_data
        };

        // Python: result[degree_column] = result[degree_column].fillna(0)
        result.data.forEach(row => {
            if (row[degree_column] === null || row[degree_column] === undefined) {
                row[degree_column] = 0;
            }
        });

        return result;
    }

    // Python: output_df = join_to_degree(edge_df, edge_source_column)
    let output_df = join_to_degree(edge_df, edge_source_column);
    // Python: output_df = join_to_degree(output_df, edge_target_column)
    output_df = join_to_degree(output_df, edge_target_column);

    // Python: output_df["combined_degree"] = (
    //     output_df[_degree_colname(edge_source_column)]
    //     + output_df[_degree_colname(edge_target_column)]
    // )
    const source_degree_col = _degree_colname(edge_source_column);
    const target_degree_col = _degree_colname(edge_target_column);

    const combined_degrees = output_df.data.map(row => {
        const source_degree = row[source_degree_col] || 0;
        const target_degree = row[target_degree_col] || 0;
        return source_degree + target_degree;
    });

    // Python: return cast("pd.Series", output_df["combined_degree"])
    return combined_degrees;
}

// Python: def _degree_colname(column: str) -> str:
//     return f"{column}_degree"
function _degree_colname(column: string): string {
    return `${column}_degree`;
}

// Compatibility export for existing code
export const computeEdgeCombinedDegree = compute_edge_combined_degree;