﻿/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing snapshot_graphml method definition.
 */

import { Graph } from '../utils/graphs';
import { PipelineStorage }

/**
 * Take a entire snapshot of a graph to standard graphml format.
 * @param input - Either a GraphML string or a Graph object
 * @param name - Name for the snapshot file
 * @param storage - Pipeline storage instance
 */
export async function snapshotGraphml(
    input: string | Graph,
    name: string,
    storage: PipelineStorage
): Promise<void> {
    let graphml: string;
    
    if (typeof input === 'string') {
        graphml = input;
    } else {
        graphml = generateGraphml(input);
    }
    
    await storage.set(`${name}.graphml`, graphml);
}

/**
 * Generate GraphML format from a Graph object.
 * This is a simplified implementation of GraphML generation.
 */
function generateGraphml(graph: Graph): string {
    const lines: string[] = [];
    
    // GraphML header
    lines.push('<?xml version="1.0" encoding="UTF-8"?>');
    lines.push('<graphml xmlns="http://graphml.graphdrawing.org/xmlns"');
    lines.push('         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"');
    lines.push('         xsi:schemaLocation="http://graphml.graphdrawing.org/xmlns');
    lines.push('         http://graphml.graphdrawing.org/xmlns/1.0/graphml.xsd">');
    
    // Graph element
    lines.push('  <graph id="G" edgedefault="undirected">');
    
    // Add nodes
    graph.nodes.forEach((nodeData, nodeId) => {
        lines.push(`    <node id="${escapeXml(nodeId)}">`);
        
        // Add node data
        Object.entries(nodeData).forEach(([key, value]) => {
            if (value != null) {
                lines.push(`      <data key="${escapeXml(key)}">${escapeXml(String(value))}</data>`);
            }
        });
        
        lines.push('    </node>');
    });
    
    // Add edges
    let edgeId = 0;
    graph.edges.forEach((edge, edgeKey) => {
        lines.push(`    <edge id="e${edgeId}" source="${escapeXml(edge.source)}" target="${escapeXml(edge.target)}">`);
        
        // Add edge weight
        if (edge.weight != null) {
            lines.push(`      <data key="weight">${edge.weight}</data>`);
        }
        
        // Add edge data
        if (edge.data) {
            Object.entries(edge.data).forEach(([key, value]) => {
                if (value != null) {
                    lines.push(`      <data key="${escapeXml(key)}">${escapeXml(String(value))}</data>`);
                }
            });
        }
        
        lines.push('    </edge>');
        edgeId++;
    });
    
    // Close graph and graphml
    lines.push('  </graph>');
    lines.push('</graphml>');
    
    return lines.join('\n');
}

/**
 * Escape XML special characters.
 */
function escapeXml(text: string): string {
    return text
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#39;');
}
