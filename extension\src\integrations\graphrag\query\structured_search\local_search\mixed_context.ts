﻿// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Algorithms to build context data for local search prompt.
 */

import { CommunityReport }
import { Covariate } from '../../../data_model/covariate';
import { Entity } from '../../../data_model/entity';
import { Relationship } from '../../../data_model/relationship';
import { TextUnit }
import { EmbeddingModel } from '../../../language_model/protocol/base';
import { ContextBuilderResult, LocalContextBuilder } from '../../context_builder/builders';
import { buildCommunityContext }
import { ConversationHistory }
import { EntityVectorStoreKey, mapQueryToEntities }
import {
  buildCovariatesContext,
  buildEntityContext,
  buildRelationshipContext,
  getCandidateContext,
} from '../../context_builder/local-context';
import { buildTextUnitContext, countRelationships }
import { getCandidateCommunities }
import { getCandidateTextUnits }
import { numTokens }
import { BaseVectorStore } from '../../../vector_stores/base';
import { DataFrame }

/**
 * Build data context for local search prompt combining community reports and entity/relationship/covariate tables.
 */
export class LocalSearchMixedContext extends LocalContextBuilder {
  private entities: Record<string, Entity>;
  private communityReports: Record<string, CommunityReport>;
  private textUnits: Record<string, TextUnit>;
  private relationships: Record<string, Relationship>;
  private covariates: Record<string, Covariate[]>;
  private entityTextEmbeddings: BaseVectorStore;
  private textEmbedder: EmbeddingModel;
  private tokenEncoder?: any;
  private embeddingVectorstoreKey: string;

  constructor(
    entities: Entity[],
    entityTextEmbeddings: BaseVectorStore,
    textEmbedder: EmbeddingModel,
    textUnits: TextUnit[] = [],
    communityReports: CommunityReport[] = [],
    relationships: Relationship[] = [],
    covariates: Record<string, Covariate[]> = {},
    tokenEncoder?: any,
    embeddingVectorstoreKey: string = EntityVectorStoreKey.ID
  ) {
    super();
    this.entities = Object.fromEntries(entities.map(entity => [entity.id, entity]));
    this.communityReports = Object.fromEntries(
      communityReports.map(community => [community.communityId, community])
    );
    this.textUnits = Object.fromEntries(textUnits.map(unit => [unit.id, unit]));
    this.relationships = Object.fromEntries(
      relationships.map(relationship => [relationship.id, relationship])
    );
    this.covariates = covariates;
    this.entityTextEmbeddings = entityTextEmbeddings;
    this.textEmbedder = textEmbedder;
    this.tokenEncoder = tokenEncoder;
    this.embeddingVectorstoreKey = embeddingVectorstoreKey;
  }

  /**
   * Filter entity text embeddings by entity keys.
   */
  filterByEntityKeys(entityKeys: (number | string)[]): void {
    this.entityTextEmbeddings.filterById(entityKeys);
  }

  buildContext(
    query: string,
    conversationHistory?: ConversationHistory,
    options: {
      includeEntityNames?: string[];
      excludeEntityNames?: string[];
      conversationHistoryMaxTurns?: number;
      conversationHistoryUserTurnsOnly?: boolean;
      maxContextTokens?: number;
      textUnitProp?: number;
      communityProp?: number;
      topKMappedEntities?: number;
      topKRelationships?: number;
      includeCommunityRank?: boolean;
      includeEntityRank?: boolean;
      rankDescription?: string;
      includeRelationshipWeight?: boolean;
      relationshipRankingAttribute?: string;
      returnCandidateContext?: boolean;
      useCommunitySummary?: boolean;
      minCommunityRank?: number;
      communityContextName?: string;
      columnDelimiter?: string;
    } = {}
  ): ContextBuilderResult {
    const {
      includeEntityNames = [],
      excludeEntityNames = [],
      conversationHistoryMaxTurns = 5,
      conversationHistoryUserTurnsOnly = true,
      maxContextTokens = 8000,
      textUnitProp = 0.5,
      communityProp = 0.25,
      topKMappedEntities = 10,
      topKRelationships = 10,
      includeCommunityRank = false,
      includeEntityRank = false,
      rankDescription = 'number of relationships',
      includeRelationshipWeight = false,
      relationshipRankingAttribute = 'rank',
      returnCandidateContext = false,
      useCommunitySummary = false,
      minCommunityRank = 0,
      communityContextName = 'Reports',
      columnDelimiter = '|'
    } = options;

    /**
     * Build data context for local search prompt.
     * 
     * Build a context by combining community reports and entity/relationship/covariate tables, 
     * and text units using a predefined ratio set by summary_prop.
     */
    if (communityProp + textUnitProp > 1) {
      throw new Error('The sum of community_prop and text_unit_prop should not exceed 1.');
    }

    // map user query to entities
    // if there is conversation history, attached the previous user questions to the current query
    let queryWithHistory = query;
    if (conversationHistory) {
      const preUserQuestions = conversationHistory
        .getUserTurns(conversationHistoryMaxTurns)
        .join('\n');
      queryWithHistory = `${query}\n${preUserQuestions}`;
    }

    const selectedEntities = mapQueryToEntities(
      queryWithHistory,
      this.entityTextEmbeddings,
      this.textEmbedder,
      this.entities,
      {
        embeddingVectorstoreKey: this.embeddingVectorstoreKey,
        includeEntityNames,
        excludeEntityNames,
        k: topKMappedEntities,
        oversampleScaler: 2
      }
    );

    // build context
    const finalContext: string[] = [];
    let finalContextData: Record<string, DataFrame> = {};
    let remainingTokens = maxContextTokens;

    if (conversationHistory) {
      // build conversation history context
      const historyResult = conversationHistory.buildContext({
        includeUserTurnsOnly: conversationHistoryUserTurnsOnly,
        maxQaTurns: conversationHistoryMaxTurns,
        columnDelimiter,
        maxContextTokens,
        recencyBias: false
      });

      if (historyResult.contextText.trim() !== '') {
        finalContext.push(historyResult.contextText);
        finalContextData = historyResult.contextData;
        remainingTokens -= numTokens(historyResult.contextText, this.tokenEncoder);
      }
    }

    // build community context
    const communityTokens = Math.max(Math.floor(remainingTokens * communityProp), 0);
    const communityResult = this.buildCommunityContext(
      selectedEntities,
      {
        maxContextTokens: communityTokens,
        useCommunitySummary,
        columnDelimiter,
        includeCommunityRank,
        minCommunityRank,
        returnCandidateContext,
        contextName: communityContextName
      }
    );

    if (communityResult.contextText.trim() !== '') {
      finalContext.push(communityResult.contextText);
      Object.assign(finalContextData, communityResult.contextData);
    }

    // build local (i.e. entity-relationship-covariate) context
    const localProp = 1 - communityProp - textUnitProp;
    const localTokens = Math.max(Math.floor(remainingTokens * localProp), 0);
    const localResult = this.buildLocalContext(
      selectedEntities,
      {
        maxContextTokens: localTokens,
        includeEntityRank,
        rankDescription,
        includeRelationshipWeight,
        topKRelationships,
        relationshipRankingAttribute,
        returnCandidateContext,
        columnDelimiter
      }
    );

    if (localResult.contextText.trim() !== '') {
      finalContext.push(localResult.contextText);
      Object.assign(finalContextData, localResult.contextData);
    }

    const textUnitTokens = Math.max(Math.floor(remainingTokens * textUnitProp), 0);
    const textUnitResult = this.buildTextUnitContext(
      selectedEntities,
      {
        maxContextTokens: textUnitTokens,
        returnCandidateContext
      }
    );

    if (textUnitResult.contextText.trim() !== '') {
      finalContext.push(textUnitResult.contextText);
      Object.assign(finalContextData, textUnitResult.contextData);
    }

    return {
      contextChunks: finalContext.join('\n\n'),
      contextRecords: finalContextData,
      llmCalls: 0,
      promptTokens: 0,
      outputTokens: 0
    };
  }

  private buildCommunityContext(
    selectedEntities: Entity[],
    options: {
      maxContextTokens?: number;
      useCommunitySummary?: boolean;
      columnDelimiter?: string;
      includeCommunityRank?: boolean;
      minCommunityRank?: number;
      returnCandidateContext?: boolean;
      contextName?: string;
    } = {}
  ): { contextText: string; contextData: Record<string, DataFrame> } {
    const {
      maxContextTokens = 4000,
      useCommunitySummary = false,
      columnDelimiter = '|',
      includeCommunityRank = false,
      minCommunityRank = 0,
      returnCandidateContext = false,
      contextName = 'Reports'
    } = options;

    /**
     * Add community data to the context window until it hits the max_context_tokens limit.
     */
    if (selectedEntities.length === 0 || Object.keys(this.communityReports).length === 0) {
      return {
        contextText: '',
        contextData: { [contextName.toLowerCase()]: { length: 0 } }
      };
    }

    const communityMatches: Record<string, number> = {};
    for (const entity of selectedEntities) {
      // increase count of the community that this entity belongs to
      if (entity.communityIds) {
        for (const communityId of entity.communityIds) {
          communityMatches[communityId] = (communityMatches[communityId] || 0) + 1;
        }
      }
    }

    // sort communities by number of matched entities and rank
    const selectedCommunities = Object.keys(communityMatches)
      .filter(communityId => communityId in this.communityReports)
      .map(communityId => {
        const community = { ...this.communityReports[communityId] };
        if (!community.attributes) {
          community.attributes = {};
        }
        community.attributes.matches = communityMatches[communityId];
        return community;
      })
      .sort((a, b) => {
        const aMatches = a.attributes?.matches || 0;
        const bMatches = b.attributes?.matches || 0;
        const aRank = a.rank || 0;
        const bRank = b.rank || 0;
        return bMatches - aMatches || bRank - aRank;
      });

    // Remove matches attribute after sorting
    selectedCommunities.forEach(community => {
      if (community.attributes?.matches) {
        delete community.attributes.matches;
      }
    });

    const contextResult = buildCommunityContext(
      selectedCommunities,
      undefined,
      {
        tokenEncoder: this.tokenEncoder,
        useCommunitySummary,
        columnDelimiter,
        shuffleData: false,
        includeCommunityRank,
        minCommunityRank,
        maxContextTokens,
        singleBatch: true,
        contextName
      }
    );

    let contextText = Array.isArray(contextResult.contextChunks)
      ? contextResult.contextChunks.join('\n\n')
      : contextResult.contextChunks;

    let contextData = contextResult.contextRecords;

    if (returnCandidateContext) {
      const candidateContextData = getCandidateCommunities(
        selectedEntities,
        Object.values(this.communityReports),
        includeCommunityRank,
        useCommunitySummary
      );

      const contextKey = contextName.toLowerCase();
      if (!(contextKey in contextData)) {
        contextData[contextKey] = candidateContextData;
        // Add in_context column
        for (let i = 0; i < candidateContextData.length; i++) {
          if (!candidateContextData.in_context) {
            candidateContextData.in_context = [];
          }
          candidateContextData.in_context[i] = false;
        }
      } else {
        // Mark which items are in context
        if (candidateContextData.id && contextData[contextKey].id) {
          const inContextIds = new Set(contextData[contextKey].id);
          candidateContextData.in_context = candidateContextData.id.map((id: string) =>
            inContextIds.has(id)
          );
          contextData[contextKey] = candidateContextData;
        } else {
          if (!contextData[contextKey].in_context) {
            contextData[contextKey].in_context = [];
          }
          for (let i = 0; i < contextData[contextKey].length; i++) {
            contextData[contextKey].in_context[i] = true;
          }
        }
      }
    }

    return { contextText, contextData };
  }

  private buildTextUnitContext(
    selectedEntities: Entity[],
    options: {
      maxContextTokens?: number;
      returnCandidateContext?: boolean;
      columnDelimiter?: string;
      contextName?: string;
    } = {}
  ): { contextText: string; contextData: Record<string, DataFrame> } {
    const {
      maxContextTokens = 8000,
      returnCandidateContext = false,
      columnDelimiter = '|',
      contextName = 'Sources'
    } = options;

    /**
     * Rank matching text units and add them to the context window until it hits the max_context_tokens limit.
     */
    if (!selectedEntities.length || !Object.keys(this.textUnits).length) {
      return {
        contextText: '',
        contextData: { [contextName.toLowerCase()]: { length: 0 } }
      };
    }

    const textUnitIdsSet = new Set<string>();
    const unitInfoList: Array<{ unit: TextUnit; entityIndex: number; numRelationships: number }> = [];
    const relationshipValues = Object.values(this.relationships);

    for (const [index, entity] of selectedEntities.entries()) {
      // get matching relationships
      const entityRelationships = relationshipValues.filter(
        rel => rel.source === entity.title || rel.target === entity.title
      );

      for (const textId of entity.textUnitIds || []) {
        if (!textUnitIdsSet.has(textId) && textId in this.textUnits) {
          const selectedUnit = { ...this.textUnits[textId] };
          const numRelationships = countRelationships(entityRelationships, selectedUnit);
          textUnitIdsSet.add(textId);
          unitInfoList.push({
            unit: selectedUnit,
            entityIndex: index,
            numRelationships
          });
        }
      }
    }

    // sort by entity_order and the number of relationships desc
    unitInfoList.sort((a, b) => {
      if (a.entityIndex !== b.entityIndex) {
        return a.entityIndex - b.entityIndex;
      }
      return b.numRelationships - a.numRelationships;
    });

    const selectedTextUnits = unitInfoList.map(info => info.unit);

    const contextResult = buildTextUnitContext(
      selectedTextUnits,
      {
        tokenEncoder: this.tokenEncoder,
        maxContextTokens,
        shuffleData: false,
        contextName,
        columnDelimiter
      }
    );

    let contextData = contextResult.contextRecords;

    if (returnCandidateContext) {
      const candidateContextData = getCandidateTextUnits(
        selectedEntities,
        Object.values(this.textUnits)
      );

      const contextKey = contextName.toLowerCase();
      if (!(contextKey in contextData)) {
        // Add in_context column
        for (let i = 0; i < candidateContextData.length; i++) {
          if (!candidateContextData.in_context) {
            candidateContextData.in_context = [];
          }
          candidateContextData.in_context[i] = false;
        }
        contextData[contextKey] = candidateContextData;
      } else {
        // Mark which items are in context
        if (candidateContextData.id && contextData[contextKey].id) {
          const inContextIds = new Set(contextData[contextKey].id);
          candidateContextData.in_context = candidateContextData.id.map((id: string) =>
            inContextIds.has(id)
          );
          contextData[contextKey] = candidateContextData;
        } else {
          if (!contextData[contextKey].in_context) {
            contextData[contextKey].in_context = [];
          }
          for (let i = 0; i < contextData[contextKey].length; i++) {
            contextData[contextKey].in_context[i] = true;
          }
        }
      }
    }

    return {
      contextText: contextResult.contextChunks,
      contextData
    };
  }

  private buildLocalContext(
    selectedEntities: Entity[],
    options: {
      maxContextTokens?: number;
      includeEntityRank?: boolean;
      rankDescription?: string;
      includeRelationshipWeight?: boolean;
      topKRelationships?: number;
      relationshipRankingAttribute?: string;
      returnCandidateContext?: boolean;
      columnDelimiter?: string;
    } = {}
  ): { contextText: string; contextData: Record<string, DataFrame> } {
    const {
      maxContextTokens = 8000,
      includeEntityRank = false,
      rankDescription = 'relationship count',
      includeRelationshipWeight = false,
      topKRelationships = 10,
      relationshipRankingAttribute = 'rank',
      returnCandidateContext = false,
      columnDelimiter = '|'
    } = options;

    /**
     * Build data context for local search prompt combining entity/relationship/covariate tables.
     */
    // build entity context
    const entityResult = buildEntityContext(
      selectedEntities,
      {
        tokenEncoder: this.tokenEncoder,
        maxContextTokens,
        columnDelimiter,
        includeEntityRank,
        rankDescription,
        contextName: 'Entities'
      }
    );

    const entityTokens = numTokens(entityResult.contextChunks, this.tokenEncoder);

    // build relationship-covariate context
    const addedEntities: Entity[] = [];
    let finalContext: string[] = [];
    let finalContextData: Record<string, DataFrame> = {};

    // gradually add entities and associated metadata to the context until we reach limit
    for (const entity of selectedEntities) {
      const currentContext: string[] = [];
      let currentContextData: Record<string, DataFrame> = {};
      addedEntities.push(entity);

      // build relationship context
      const relationshipResult = buildRelationshipContext(
        addedEntities,
        Object.values(this.relationships),
        {
          tokenEncoder: this.tokenEncoder,
          maxContextTokens,
          columnDelimiter,
          topKRelationships,
          includeRelationshipWeight,
          relationshipRankingAttribute,
          contextName: 'Relationships'
        }
      );

      currentContext.push(relationshipResult.contextChunks);
      currentContextData.relationships = relationshipResult.contextRecords;
      let totalTokens = entityTokens + numTokens(relationshipResult.contextChunks, this.tokenEncoder);

      // build covariate context
      for (const [covariateType, covariateList] of Object.entries(this.covariates)) {
        const covariateResult = buildCovariatesContext(
          addedEntities,
          covariateList,
          {
            tokenEncoder: this.tokenEncoder,
            maxContextTokens,
            columnDelimiter,
            contextName: covariateType
          }
        );

        totalTokens += numTokens(covariateResult.contextChunks, this.tokenEncoder);
        currentContext.push(covariateResult.contextChunks);
        currentContextData[covariateType.toLowerCase()] = covariateResult.contextRecords;
      }

      if (totalTokens > maxContextTokens) {
        console.warn('Reached token limit - reverting to previous context state');
        break;
      }

      finalContext = currentContext;
      finalContextData = currentContextData;
    }

    // attach entity context to final context
    const finalContextText = `${entityResult.contextChunks}\n\n${finalContext.join('\n\n')}`;
    finalContextData.entities = entityResult.contextRecords;

    if (returnCandidateContext) {
      // we return all the candidate entities/relationships/covariates (not only those that were fitted into the context window)
      // and add a tag to indicate which records were included in the context window
      const candidateContextData = getCandidateContext(
        selectedEntities,
        Object.values(this.entities),
        Object.values(this.relationships),
        this.covariates,
        {
          includeEntityRank,
          entityRankDescription: rankDescription,
          includeRelationshipWeight
        }
      );

      for (const [key, candidateDF] of Object.entries(candidateContextData)) {
        if (!(key in finalContextData)) {
          finalContextData[key] = candidateDF;
          // Add in_context column
          for (let i = 0; i < candidateDF.length; i++) {
            if (!candidateDF.in_context) {
              candidateDF.in_context = [];
            }
            candidateDF.in_context[i] = false;
          }
        } else {
          const inContextDF = finalContextData[key];

          if (inContextDF.id && candidateDF.id) {
            const inContextIds = new Set(inContextDF.id);
            candidateDF.in_context = candidateDF.id.map((id: string) => inContextIds.has(id));
            finalContextData[key] = candidateDF;
          } else {
            if (!finalContextData[key].in_context) {
              finalContextData[key].in_context = [];
            }
            for (let i = 0; i < finalContextData[key].length; i++) {
              finalContextData[key].in_context[i] = true;
            }
          }
        }
      }
    } else {
      for (const key of Object.keys(finalContextData)) {
        if (!finalContextData[key].in_context) {
          finalContextData[key].in_context = [];
        }
        for (let i = 0; i < finalContextData[key].length; i++) {
          finalContextData[key].in_context[i] = true;
        }
      }
    }

    return { contextText: finalContextText, contextData: finalContextData };
  }
}
