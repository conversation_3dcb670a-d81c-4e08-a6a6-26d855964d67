// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Parameterization settings for the default configuration, loaded from environment variables.
 */

import * as path from 'path';
import { GraphRagConfig, createGraphRagConfig as createConfig } from './models/graph-rag-config';

/**
 * Load Configuration Parameters from a dictionary.
 *
 * @param values - Dictionary of configuration values to pass into the config model.
 * @param rootDir - Root directory for the project.
 * @returns The configuration object.
 * @throws Error if the configuration values do not satisfy validation.
 */
export function createGraphragConfig(
    values?: Record<string, any>,
    rootDir?: string
): GraphRagConfig {
    const configValues = values || {};
    
    if (rootDir) {
        const rootPath = path.resolve(rootDir);
        configValues.root_dir = rootPath;
    }
    
    return createConfig(configValues);
}