/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing runWorkflow method definition.
 */

import { PipelineCache }
import { WorkflowCallbacks }
import { GraphRagConfig }
import { getUpdateStorages } from '../run/utils';
import { PipelineRunContext } from '../typing/context';
import { WorkflowFunctionOutput } from '../typing/workflow';
import { groupAndResolveEntities } from '../update/entities';
import { updateAndMergeRelationships } from '../update/relationships';
import { getSummarizedEntitiesRelationships }
import { PipelineStorage }
import { loadTableFromStorage, writeTableToStorage } from '../../utils/storage';

const logger = console;

export interface DataFrame {
    [key: string]: any[];
    length: number;
}

export interface UpdateEntitiesRelationshipsResult {
    mergedEntitiesDF: DataFrame;
    mergedRelationshipsDF: DataFrame;
    entityIdMapping: Record<string, any>;
}

/**
 * Update the entities and relationships from a incremental index run.
 */
export async function runWorkflow(
    config: GraphRagConfig,
    context: PipelineRunContext,
): Promise<WorkflowFunctionOutput> {
    logger.info("Workflow started: update_entities_relationships");
    
    const { outputStorage, previousStorage, deltaStorage } = getUpdateStorages(
        config, 
        context.state["update_timestamp"]
    );

    const {
        mergedEntitiesDF,
        mergedRelationshipsDF,
        entityIdMapping,
    } = await updateEntitiesAndRelationships(
        previousStorage,
        deltaStorage,
        outputStorage,
        config,
        context.cache,
        context.callbacks,
    );

    context.state["incremental_update_merged_entities"] = mergedEntitiesDF;
    context.state["incremental_update_merged_relationships"] = mergedRelationshipsDF;
    context.state["incremental_update_entity_id_mapping"] = entityIdMapping;

    logger.info("Workflow completed: update_entities_relationships");
    return { result: null };
}

/**
 * Update Final Entities and Relationships output.
 */
async function updateEntitiesAndRelationships(
    previousStorage: PipelineStorage,
    deltaStorage: PipelineStorage,
    outputStorage: PipelineStorage,
    config: GraphRagConfig,
    cache: PipelineCache,
    callbacks: WorkflowCallbacks,
): Promise<UpdateEntitiesRelationshipsResult> {
    const oldEntities = await loadTableFromStorage("entities", previousStorage);
    const deltaEntities = await loadTableFromStorage("entities", deltaStorage);

    const { mergedEntitiesDF, entityIdMapping } = groupAndResolveEntities(
        oldEntities, 
        deltaEntities
    );

    // Update Relationships
    const oldRelationships = await loadTableFromStorage("relationships", previousStorage);
    const deltaRelationships = await loadTableFromStorage("relationships", deltaStorage);
    let mergedRelationshipsDF = updateAndMergeRelationships(
        oldRelationships,
        deltaRelationships,
    );

    const summarizationLlmSettings = config.getLanguageModelConfig(
        config.summarizeDescriptions.modelId
    );
    const summarizationStrategy = config.summarizeDescriptions.resolvedStrategy(
        config.rootDir, 
        summarizationLlmSettings
    );

    const {
        extractedEntities: finalMergedEntitiesDF,
        extractedRelationships: finalMergedRelationshipsDF,
    } = await getSummarizedEntitiesRelationships({
        extractedEntities: mergedEntitiesDF,
        extractedRelationships: mergedRelationshipsDF,
        callbacks,
        cache,
        summarizationStrategy,
        summarizationNumThreads: summarizationLlmSettings.concurrentRequests,
    });

    // Save the updated entities back to storage
    await writeTableToStorage(finalMergedEntitiesDF, "entities", outputStorage);
    await writeTableToStorage(finalMergedRelationshipsDF, "relationships", outputStorage);

    return {
        mergedEntitiesDF: finalMergedEntitiesDF,
        mergedRelationshipsDF: finalMergedRelationshipsDF,
        entityIdMapping,
    };
}
