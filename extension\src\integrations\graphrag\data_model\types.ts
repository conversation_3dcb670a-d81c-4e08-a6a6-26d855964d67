/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 * 
 * Common types for the GraphRAG knowledge model.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

/**
 * Type for text embedding function.
 */
export type TextEmbedder = (text: string) => Promise<number[]>

/**
 * Synchronous version of text embedder.
 */
export type TextEmbedderSync = (text: string) => number[]