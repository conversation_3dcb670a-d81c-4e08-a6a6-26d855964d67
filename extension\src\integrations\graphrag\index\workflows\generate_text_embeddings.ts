﻿/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing runWorkflow method definition.
 */

import { PipelineCache }
import { WorkflowCallbacks }
import {
    COMMUNITY_FULL_CONTENT_EMBEDDING,
    COMMUNITY_SUMMARY_EMBEDDING,
    COMMUNITY_TITLE_EMBEDDING,
    DOCUMENT_TEXT_EMBEDDING,
    ENTITY_DESCRIPTION_EMBEDDING,
    ENTITY_TITLE_EMBEDDING,
    RELATIONSHIP_DESCRIPTION_EMBEDDING,
    TEXT_UNIT_TEXT_EMBEDDING,
} from '../../config/embeddings';
import { getEmbeddingSettings }
import { GraphRagConfig }
import { embedText } from '../operations/embed_text';
import { PipelineRunContext } from '../typing/context';
import { WorkflowFunctionOutput } from '../typing/workflow';
import {
    loadTableFromStorage,
    storageHasTable,
    writeTableToStorage,
} from '../../utils/storage';

const logger = console;

export interface DataFrame {
    [key: string]: any[];
    length: number;
}

/**
 * All the steps to transform community reports.
 */
export async function runWorkflow(
    config: GraphRagConfig,
    context: PipelineRunContext,
): Promise<WorkflowFunctionOutput> {
    logger.info("Workflow started: generate_text_embeddings");
    
    let documents: DataFrame | null = null;
    let relationships: DataFrame | null = null;
    let textUnits: DataFrame | null = null;
    let entities: DataFrame | null = null;
    let communityReports: DataFrame | null = null;

    if (await storageHasTable("documents", context.outputStorage)) {
        documents = await loadTableFromStorage("documents", context.outputStorage);
    }
    if (await storageHasTable("relationships", context.outputStorage)) {
        relationships = await loadTableFromStorage("relationships", context.outputStorage);
    }
    if (await storageHasTable("text_units", context.outputStorage)) {
        textUnits = await loadTableFromStorage("text_units", context.outputStorage);
    }
    if (await storageHasTable("entities", context.outputStorage)) {
        entities = await loadTableFromStorage("entities", context.outputStorage);
    }
    if (await storageHasTable("community_reports", context.outputStorage)) {
        communityReports = await loadTableFromStorage("community_reports", context.outputStorage);
    }

    const embeddedFields = config.embedText.names;
    const textEmbed = getEmbeddingSettings(config);

    const output = await generateTextEmbeddings({
        documents,
        relationships,
        textUnits,
        entities,
        communityReports,
        callbacks: context.callbacks,
        cache: context.cache,
        textEmbedConfig: textEmbed,
        embeddedFields,
    });

    if (config.snapshots.embeddings) {
        for (const [name, table] of Object.entries(output)) {
            await writeTableToStorage(
                table,
                `embeddings.${name}`,
                context.outputStorage,
            );
        }
    }

    logger.info("Workflow completed: generate_text_embeddings");
    return { result: output };
}

export interface GenerateTextEmbeddingsParams {
    documents: DataFrame | null;
    relationships: DataFrame | null;
    textUnits: DataFrame | null;
    entities: DataFrame | null;
    communityReports: DataFrame | null;
    callbacks: WorkflowCallbacks;
    cache: PipelineCache;
    textEmbedConfig: Record<string, any>;
    embeddedFields: string[];
}

/**
 * All the steps to generate all embeddings.
 */
export async function generateTextEmbeddings({
    documents,
    relationships,
    textUnits,
    entities,
    communityReports,
    callbacks,
    cache,
    textEmbedConfig,
    embeddedFields,
}: GenerateTextEmbeddingsParams): Promise<Record<string, DataFrame>> {
    const embeddingParamMap: Record<string, { data: DataFrame | null; embedColumn: string }> = {
        [DOCUMENT_TEXT_EMBEDDING]: {
            data: documents ? selectColumns(documents, ["id", "text"]) : null,
            embedColumn: "text",
        },
        [RELATIONSHIP_DESCRIPTION_EMBEDDING]: {
            data: relationships ? selectColumns(relationships, ["id", "description"]) : null,
            embedColumn: "description",
        },
        [TEXT_UNIT_TEXT_EMBEDDING]: {
            data: textUnits ? selectColumns(textUnits, ["id", "text"]) : null,
            embedColumn: "text",
        },
        [ENTITY_TITLE_EMBEDDING]: {
            data: entities ? selectColumns(entities, ["id", "title"]) : null,
            embedColumn: "title",
        },
        [ENTITY_DESCRIPTION_EMBEDDING]: {
            data: entities ? createTitleDescriptionData(entities) : null,
            embedColumn: "title_description",
        },
        [COMMUNITY_TITLE_EMBEDDING]: {
            data: communityReports ? selectColumns(communityReports, ["id", "title"]) : null,
            embedColumn: "title",
        },
        [COMMUNITY_SUMMARY_EMBEDDING]: {
            data: communityReports ? selectColumns(communityReports, ["id", "summary"]) : null,
            embedColumn: "summary",
        },
        [COMMUNITY_FULL_CONTENT_EMBEDDING]: {
            data: communityReports ? selectColumns(communityReports, ["id", "full_content"]) : null,
            embedColumn: "full_content",
        },
    };

    logger.info("Creating embeddings");
    const outputs: Record<string, DataFrame> = {};
    
    for (const field of embeddedFields) {
        const params = embeddingParamMap[field];
        if (!params) {
            logger.warning(`Unknown embedding field: ${field}`);
            continue;
        }
        
        if (params.data === null) {
            const msg = `Embedding ${field} is specified but data table is not in storage. This may or may not be intentional - if you expect it to be here, please check for errors earlier in the logs.`;
            logger.warn(msg);
        } else {
            outputs[field] = await runEmbeddings({
                name: field,
                data: params.data,
                embedColumn: params.embedColumn,
                callbacks,
                cache,
                textEmbedConfig,
            });
        }
    }
    
    return outputs;
}

export interface RunEmbeddingsParams {
    name: string;
    data: DataFrame;
    embedColumn: string;
    callbacks: WorkflowCallbacks;
    cache: PipelineCache;
    textEmbedConfig: Record<string, any>;
}

/**
 * All the steps to generate single embedding.
 */
async function runEmbeddings({
    name,
    data,
    embedColumn,
    callbacks,
    cache,
    textEmbedConfig,
}: RunEmbeddingsParams): Promise<DataFrame> {
    const embeddings = await embedText({
        input: data,
        callbacks,
        cache,
        embedColumn,
        embeddingName: name,
        strategy: textEmbedConfig.strategy,
    });

    const result = { ...data };
    result.embedding = embeddings;

    return selectColumns(result, ["id", "embedding"]);
}

/**
 * Select specific columns from a DataFrame.
 */
function selectColumns(df: DataFrame, columns: string[]): DataFrame {
    const result: DataFrame = { length: df.length };
    
    for (const col of columns) {
        if (df[col] !== undefined) {
            result[col] = [...df[col]]; // Create a copy
        } else {
            result[col] = new Array(df.length).fill(null);
        }
    }

    return result;
}

/**
 * Create title_description data by combining title and description.
 */
function createTitleDescriptionData(entities: DataFrame): DataFrame {
    const result = selectColumns(entities, ["id", "title", "description"]);
    
    const titles = result.title || [];
    const descriptions = result.description || [];
    const titleDescriptions: string[] = [];
    
    for (let i = 0; i < result.length; i++) {
        const title = titles[i] || "";
        const description = descriptions[i] || "";
        titleDescriptions.push(`${title}:${description}`);
    }
    
    result.title_description = titleDescriptions;
    
    return result;
}
