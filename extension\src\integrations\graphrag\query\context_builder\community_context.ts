﻿/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Community Context.
 */

import { CommunityReport }
import { Entity } from '../../data_model/entity';
import { numTokens }

const logger = console;

const NO_COMMUNITY_RECORDS_WARNING = 
    "Warning: No community records added when building community context.";

export interface DataFrame {
    [key: string]: any[];
    length: number;
}

export interface BuildCommunityContextOptions {
    communityReports: CommunityReport[];
    entities?: Entity[];
    tokenEncoder?: any;
    useCommunitySummary?: boolean;
    columnDelimiter?: string;
    shuffleData?: boolean;
    includeCommunityRank?: boolean;
    minCommunityRank?: number;
    communityRankName?: string;
    includeCommunityWeight?: boolean;
    communityWeightName?: string;
    normalizeCommunityWeight?: boolean;
    maxContextTokens?: number;
    singleBatch?: boolean;
    contextName?: string;
    randomState?: number;
}

export function buildCommunityContext(options: BuildCommunityContextOptions): [string | string[], Record<string, DataFrame>] {
    const {
        communityReports,
        entities,
        tokenEncoder,
        useCommunitySummary = true,
        columnDelimiter = "|",
        shuffleData = true,
        includeCommunityRank = false,
        minCommunityRank = 0,
        communityRankName = "rank",
        includeCommunityWeight = true,
        communityWeightName = "occurrence weight",
        normalizeCommunityWeight = true,
        maxContextTokens = 8000,
        singleBatch = true,
        contextName = "Reports",
        randomState = 86
    } = options;

    function isIncluded(report: CommunityReport): boolean {
        return report.rank !== undefined && report.rank >= minCommunityRank;
    }

    function getHeader(attributes: string[]): string[] {
        let header = ["id", "title"];
        attributes = attributes.filter(col => !header.includes(col));
        if (!includeCommunityWeight) {
            attributes = attributes.filter(col => col !== communityWeightName);
        }
        header = header.concat(attributes);
        header.push(useCommunitySummary ? "summary" : "content");
        if (includeCommunityRank) {
            header.push(communityRankName);
        }
        return header;
    }

    function reportContextText(report: CommunityReport, attributes: string[]): [string, string[]] {
        const context: string[] = [
            report.shortId || "",
            report.title,
            ...attributes.map(field => 
                report.attributes ? String(report.attributes[field] || "") : ""
            ),
        ];
        context.push(useCommunitySummary ? report.summary : report.fullContent);
        if (includeCommunityRank) {
            context.push(String(report.rank));
        }
        const result = context.join(columnDelimiter) + "\n";
        return [result, context];
    }

    const computeCommunityWeights = (
        entities &&
        communityReports.length > 0 &&
        includeCommunityWeight &&
        (!communityReports[0].attributes || 
         !communityReports[0].attributes[communityWeightName])
    );

    let processedReports = communityReports;
    if (computeCommunityWeights) {
        logger.debug("Computing community weights...");
        processedReports = computeCommunityWeights_(
            communityReports,
            entities!,
            communityWeightName,
            normalizeCommunityWeight
        );
    }

    const selectedReports = processedReports.filter(isIncluded);

    if (!selectedReports || selectedReports.length === 0) {
        return [[], {}];
    }

    if (shuffleData) {
        // Simple shuffle implementation
        for (let i = selectedReports.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [selectedReports[i], selectedReports[j]] = [selectedReports[j], selectedReports[i]];
        }
    }

    const attributes = communityReports[0].attributes ? Object.keys(communityReports[0].attributes) : [];
    const header = getHeader(attributes);
    const allContextText: string[] = [];
    const allContextRecords: DataFrame[] = [];

    let batchText = "";
    let batchTokens = 0;
    let batchRecords: string[][] = [];

    function initBatch(): void {
        batchText = `-----${contextName}-----\n${header.join(columnDelimiter)}\n`;
        batchTokens = numTokens(batchText, tokenEncoder);
        batchRecords = [];
    }

    function cutBatch(): void {
        const recordDF = convertReportContextToDF(
            batchRecords,
            header,
            entities && includeCommunityWeight ? communityWeightName : undefined,
            includeCommunityRank ? communityRankName : undefined
        );
        
        if (recordDF.length === 0) {
            return;
        }

        let currentContextText = arrayToCSV(recordDF, header, columnDelimiter);
        if (allContextText.length === 0 && singleBatch) {
            currentContextText = `-----${contextName}-----\n${currentContextText}`;
        }

        allContextText.push(currentContextText);
        allContextRecords.push(recordDF);
    }

    initBatch();

    for (const report of selectedReports) {
        const [newContextText, newContext] = reportContextText(report, attributes);
        const newTokens = numTokens(newContextText, tokenEncoder);

        if (batchTokens + newTokens > maxContextTokens) {
            cutBatch();
            if (singleBatch) {
                break;
            }
            initBatch();
        }

        batchText += newContextText;
        batchTokens += newTokens;
        batchRecords.push(newContext);
    }

    const currentBatchIds = new Set(batchRecords.map(record => record[0]));
    const existingIdsSets = allContextRecords.map(record => 
        new Set(record.id || [])
    );

    const hasCurrentBatch = existingIdsSets.some(existingIds => 
        currentBatchIds.size === existingIds.size && 
        [...currentBatchIds].every(id => existingIds.has(id))
    );

    if (!hasCurrentBatch) {
        cutBatch();
    }

    if (allContextRecords.length === 0) {
        logger.warn(NO_COMMUNITY_RECORDS_WARNING);
        return [[], {}];
    }

    return [allContextText, {
        [contextName.toLowerCase()]: concatenateDataFrames(allContextRecords)
    }];
}

function computeCommunityWeights_(
    communityReports: CommunityReport[],
    entities: Entity[],
    weightAttribute: string = "occurrence",
    normalize: boolean = true
): CommunityReport[] {
    if (!entities) {
        return communityReports;
    }

    const communityTextUnits: Record<string, string[]> = {};
    
    for (const entity of entities) {
        if (entity.communityIds) {
            for (const communityId of entity.communityIds) {
                if (!communityTextUnits[communityId]) {
                    communityTextUnits[communityId] = [];
                }
                communityTextUnits[communityId].push(...entity.textUnitIds);
            }
        }
    }

    for (const report of communityReports) {
        if (!report.attributes) {
            report.attributes = {};
        }
        const textUnits = communityTextUnits[report.communityId] || [];
        report.attributes[weightAttribute] = new Set(textUnits).size;
    }

    if (normalize) {
        const allWeights = communityReports
            .filter(report => report.attributes)
            .map(report => report.attributes![weightAttribute]);
        const maxWeight = Math.max(...allWeights);
        
        for (const report of communityReports) {
            if (report.attributes) {
                report.attributes[weightAttribute] = report.attributes[weightAttribute] / maxWeight;
            }
        }
    }

    return communityReports;
}

function convertReportContextToDF(
    contextRecords: string[][],
    header: string[],
    weightColumn?: string,
    rankColumn?: string
): DataFrame {
    if (contextRecords.length === 0) {
        return { length: 0 };
    }

    const result: DataFrame = { length: contextRecords.length };
    
    for (let i = 0; i < header.length; i++) {
        const columnName = header[i];
        result[columnName] = contextRecords.map(record => record[i]);
    }

    return rankReportContext(result, weightColumn, rankColumn);
}

function rankReportContext(
    reportDF: DataFrame,
    weightColumn?: string,
    rankColumn?: string
): DataFrame {
    const rankAttributes: string[] = [];
    
    if (weightColumn && reportDF[weightColumn]) {
        rankAttributes.push(weightColumn);
        reportDF[weightColumn] = reportDF[weightColumn].map((val: any) => parseFloat(String(val)));
    }
    
    if (rankColumn && reportDF[rankColumn]) {
        rankAttributes.push(rankColumn);
        reportDF[rankColumn] = reportDF[rankColumn].map((val: any) => parseFloat(String(val)));
    }

    if (rankAttributes.length > 0) {
        // Simple sorting implementation
        const indices = Array.from({ length: reportDF.length }, (_, i) => i);
        indices.sort((a, b) => {
            for (const attr of rankAttributes) {
                const valA = reportDF[attr][a];
                const valB = reportDF[attr][b];
                if (valA !== valB) {
                    return valB - valA; // Descending order
                }
            }
            return 0;
        });

        // Reorder all columns based on sorted indices
        for (const [key, values] of Object.entries(reportDF)) {
            if (key !== 'length') {
                reportDF[key] = indices.map(i => values[i]);
            }
        }
    }

    return reportDF;
}

function arrayToCSV(df: DataFrame, header: string[], delimiter: string): string {
    if (df.length === 0) return "";
    
    const rows: string[] = [header.join(delimiter)];
    
    for (let i = 0; i < df.length; i++) {
        const row = header.map(col => String(df[col][i] || ""));
        rows.push(row.join(delimiter));
    }
    
    return rows.join("\n");
}

function concatenateDataFrames(dataframes: DataFrame[]): DataFrame {
    if (dataframes.length === 0) {
        return { length: 0 };
    }

    const result: DataFrame = { length: 0 };
    
    // Get all unique column names
    const allColumns = new Set<string>();
    for (const df of dataframes) {
        Object.keys(df).forEach(key => {
            if (key !== 'length') {
                allColumns.add(key);
            }
        });
    }

    // Calculate total length
    const totalLength = dataframes.reduce((sum, df) => sum + df.length, 0);
    result.length = totalLength;

    // Concatenate each column
    for (const column of allColumns) {
        const concatenatedColumn: any[] = [];
        
        for (const df of dataframes) {
            const columnData = df[column] || new Array(df.length).fill(null);
            concatenatedColumn.push(...columnData);
        }
        
        result[column] = concatenatedColumn;
    }

    return result;
}
