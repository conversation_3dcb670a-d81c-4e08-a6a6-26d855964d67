#!/usr/bin/env python3
"""
脚本用于批量替换TypeScript/JavaScript文件中import语句里的连字符(-)为下划线(_)
只处理import语句，不影响其他代码
"""

import os
import re
import sys
from pathlib import Path

def replace_import_hyphens(file_path):
    """
    替换单个文件中import语句里的连字符
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        changed = False
        new_lines = []

        for line in lines:
            original_line = line

            # 检查是否是import语句
            if 'import' in line and 'from' in line:
                # 匹配import语句中的路径部分
                # 支持单引号和双引号
                import_pattern = r"(import[^;]*?from\s+['\"])([^'\"]*?)(['\"])"

                def replace_hyphens_in_path(match):
                    prefix = match.group(1)  # import ... from '
                    path = match.group(2)    # 路径部分
                    suffix = match.group(3)  # ' 或 "

                    # 只替换路径中的连字符为下划线
                    new_path = path.replace('-', '_')
                    return prefix + new_path + suffix

                # 执行替换
                line = re.sub(import_pattern, replace_hyphens_in_path, line)

                if line != original_line:
                    changed = True
                    print(f"  替换: {original_line.strip()} -> {line.strip()}")

            new_lines.append(line)

        # 如果内容有变化，写回文件
        if changed:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.writelines(new_lines)
            return True

        return False

    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
        return False

def find_and_process_files(root_dir):
    """
    查找并处理所有TypeScript和JavaScript文件
    """
    processed_count = 0
    changed_count = 0
    
    # 支持的文件扩展名
    extensions = ['.ts', '.js', '.tsx', '.jsx']
    
    for root, dirs, files in os.walk(root_dir):
        for file in files:
            if any(file.endswith(ext) for ext in extensions):
                file_path = os.path.join(root, file)
                processed_count += 1
                
                if replace_import_hyphens(file_path):
                    changed_count += 1
                    print(f"已处理: {file_path}")
                
                # 每处理100个文件显示进度
                if processed_count % 100 == 0:
                    print(f"已处理 {processed_count} 个文件，修改了 {changed_count} 个文件")
    
    return processed_count, changed_count

def main():
    # 获取当前脚本所在目录
    script_dir = Path(__file__).parent
    
    # 默认处理当前目录
    target_dir = script_dir
    
    # 如果提供了命令行参数，使用指定目录
    if len(sys.argv) > 1:
        target_dir = Path(sys.argv[1])
    
    if not target_dir.exists():
        print(f"错误: 目录 {target_dir} 不存在")
        sys.exit(1)
    
    print(f"开始处理目录: {target_dir}")
    print("正在查找并处理TypeScript/JavaScript文件...")
    
    processed_count, changed_count = find_and_process_files(target_dir)
    
    print(f"\n处理完成!")
    print(f"总共处理了 {processed_count} 个文件")
    print(f"修改了 {changed_count} 个文件")

if __name__ == "__main__":
    main()
