﻿/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Context Build utility methods.
 */

import { Relationship } from '../../data_model/relationship';
import { TextUnit }
import { numTokens }

export interface DataFrame {
    [key: string]: any[];
    length: number;
}

export interface BuildTextUnitContextOptions {
    textUnits: TextUnit[];
    tokenEncoder?: any;
    columnDelimiter?: string;
    shuffleData?: boolean;
    maxContextTokens?: number;
    contextName?: string;
    randomState?: number;
}

export function buildTextUnitContext(options: BuildTextUnitContextOptions): [string, Record<string, DataFrame>] {
    const {
        textUnits,
        tokenEncoder,
        columnDelimiter = "|",
        shuffleData = true,
        maxContextTokens = 8000,
        contextName = "Sources",
        randomState = 86
    } = options;

    if (!textUnits || textUnits.length === 0) {
        return ["", {}];
    }

    let processedTextUnits = [...textUnits];
    if (shuffleData) {
        // Simple shuffle implementation using the random state as seed
        const random = () => {
            const x = Math.sin(randomState) * 10000;
            return x - Math.floor(x);
        };
        
        for (let i = processedTextUnits.length - 1; i > 0; i--) {
            const j = Math.floor(random() * (i + 1));
            [processedTextUnits[i], processedTextUnits[j]] = [processedTextUnits[j], processedTextUnits[i]];
        }
    }

    // Add context header
    let currentContextText = `-----${contextName}-----\n`;

    // Add header
    const header = ["id", "text"];
    const attributeCols = processedTextUnits[0].attributes
        ? Object.keys(processedTextUnits[0].attributes).filter(col => !header.includes(col))
        : [];
    header.push(...attributeCols);

    currentContextText += header.join(columnDelimiter) + "\n";
    let currentTokens = numTokens(currentContextText, tokenEncoder);
    const allContextRecords = [header];

    for (const unit of processedTextUnits) {
        const newContext = [
            unit.shortId,
            unit.text,
            ...attributeCols.map(field => 
                unit.attributes && unit.attributes[field] 
                    ? String(unit.attributes[field]) 
                    : ""
            ),
        ];

        const newContextText = newContext.join(columnDelimiter) + "\n";
        const newTokens = numTokens(newContextText, tokenEncoder);

        if (currentTokens + newTokens > maxContextTokens) {
            break;
        }

        currentContextText += newContextText;
        allContextRecords.push(newContext);
        currentTokens += newTokens;
    }

    let recordDF: DataFrame;
    if (allContextRecords.length > 1) {
        recordDF = { length: allContextRecords.length - 1 };
        for (let i = 0; i < header.length; i++) {
            const columnName = header[i];
            recordDF[columnName] = allContextRecords.slice(1).map(record => record[i]);
        }
    } else {
        recordDF = { length: 0 };
    }

    return [currentContextText, { [contextName.toLowerCase()]: recordDF }];
}

export function countRelationships(
    entityRelationships: Relationship[],
    textUnit: TextUnit
): number {
    if (!textUnit.relationshipIds) {
        // Use array method to count relationships where the textUnit.id is in rel.textUnitIds
        return entityRelationships.filter(rel => 
            rel.textUnitIds && rel.textUnitIds.includes(textUnit.id)
        ).length;
    }

    // Use a set for faster lookups if entityRelationships is large
    const entityRelationshipIds = new Set(entityRelationships.map(rel => rel.id));

    // Count matching relationship ids efficiently
    return textUnit.relationshipIds.filter(relId => 
        entityRelationshipIds.has(relId)
    ).length;
}
