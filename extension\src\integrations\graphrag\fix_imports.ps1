# PowerShell脚本：批量替换import语句中的连字符为下划线
# 只处理import语句，不影响其他代码

Write-Host "开始批量替换import语句中的连字符为下划线..." -ForegroundColor Green

$totalFiles = 0
$changedFiles = 0

# 获取所有TypeScript和JavaScript文件
$files = Get-ChildItem -Path . -Recurse -Include "*.ts", "*.js", "*.tsx", "*.jsx"

foreach ($file in $files) {
    $totalFiles++
    $lines = Get-Content $file.FullName -Encoding UTF8
    $changed = $false
    $newLines = @()

    foreach ($line in $lines) {
        $originalLine = $line

        # 检查是否是import语句并包含连字符
        if ($line -match "import.*from.*[`"'].*-.*[`"']") {
            # 简单直接的替换：在from后面的引号内容中替换所有连字符为下划线
            $newLine = $line -replace "(from\s*[`"'])([^`"']*?)([`"'])", {
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            }

            if ($newLine -ne $originalLine) {
                $changed = $true
                Write-Host "  $($file.Name): $originalLine" -ForegroundColor Gray
                Write-Host "  ->           $newLine" -ForegroundColor Yellow
            }
            $newLines += $newLine
        } else {
            $newLines += $line
        }
    }

    # 如果有变化，写回文件
    if ($changed) {
        $newLines | Set-Content -Path $file.FullName -Encoding UTF8
        $changedFiles++
        Write-Host "已处理: $($file.FullName)" -ForegroundColor Green
    }

    # 每处理50个文件显示进度
    if ($totalFiles % 50 -eq 0) {
        Write-Host "已处理 $totalFiles 个文件，修改了 $changedFiles 个文件" -ForegroundColor Cyan
    }
}

Write-Host "`n处理完成!" -ForegroundColor Green
Write-Host "总共处理了 $totalFiles 个文件" -ForegroundColor White
Write-Host "修改了 $changedFiles 个文件" -ForegroundColor White
