# PowerShell脚本：批量替换import语句中的连字符为下划线
# 只处理import语句，不影响其他代码

Write-Host "开始批量替换import语句中的连字符为下划线..." -ForegroundColor Green

$totalFiles = 0
$changedFiles = 0

# 获取所有TypeScript和JavaScript文件
$files = Get-ChildItem -Path . -Recurse -Include "*.ts", "*.js", "*.tsx", "*.jsx"

foreach ($file in $files) {
    $totalFiles++
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    $originalContent = $content
    
    # 使用正则表达式替换import语句中的连字符
    # 匹配 import ... from '...' 或 import ... from "..."
    $pattern = "(import[^;]*?from\s*[`"'])([^`"']*?)([`"'])"
    
    $content = [regex]::Replace($content, $pattern, {
        param($match)
        $prefix = $match.Groups[1].Value
        $path = $match.Groups[2].Value
        $suffix = $match.Groups[3].Value
        
        # 只替换路径中的连字符为下划线
        $newPath = $path -replace '-', '_'
        return $prefix + $newPath + $suffix
    })
    
    # 如果内容有变化，写回文件
    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8 -NoNewline
        $changedFiles++
        Write-Host "已处理: $($file.FullName)" -ForegroundColor Yellow
    }
    
    # 每处理100个文件显示进度
    if ($totalFiles % 100 -eq 0) {
        Write-Host "已处理 $totalFiles 个文件，修改了 $changedFiles 个文件" -ForegroundColor Cyan
    }
}

Write-Host "`n处理完成!" -ForegroundColor Green
Write-Host "总共处理了 $totalFiles 个文件" -ForegroundColor White
Write-Host "修改了 $changedFiles 个文件" -ForegroundColor White
