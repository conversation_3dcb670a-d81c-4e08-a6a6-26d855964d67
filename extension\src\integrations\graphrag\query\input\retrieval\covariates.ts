﻿// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Util functions to retrieve covariates from a collection.
 */

import { Covariate } from '../../../data_model/covariate';
import { Entity } from '../../../data_model/entity';
import { DataFrame } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;

/**
 * Get all covariates that are related to selected entities.
 */
export function getCandidateCovariates(
  selectedEntities: Entity[],
  covariates: Covariate[]
): Covariate[] {
  const selectedEntityNames = selectedEntities.map(entity => entity.title);
  
  return covariates.filter(covariate =>
    selectedEntityNames.includes(covariate.subjectId)
  );
}

/**
 * Convert a list of covariates to a pandas dataframe.
 */
export function toCovariateDataframe(covariates: Covariate[]): DataFrame {
  if (covariates.length === 0) {
    return { length: 0 };
  }

  // Add header
  const header = ['id', 'entity'];
  const attributes = covariates.length > 0 ? (covariates[0].attributes || {}) : {};
  const attributeCols = Object.keys(attributes).filter(col => !header.includes(col));
  header.push(...attributeCols);

  const dataFrame: DataFrame = { length: covariates.length };
  
  // Initialize columns
  header.forEach(col => {
    dataFrame[col] = [];
  });

  // Fill data
  covariates.forEach(covariate => {
    dataFrame.id.push(covariate.shortId || '');
    dataFrame.entity.push(covariate.subjectId);
    
    // Add attribute columns
    attributeCols.forEach(field => {
      const value = covariate.attributes?.[field];
      dataFrame[field].push(value ? String(value) : '');
    });
  });

  return dataFrame;
}
