﻿/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing run_workflow method definition.
 */

import { DataFrame } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { GraphRagConfig } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { InputConfig } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { createInput } from '../input/factory';
import { PipelineRunContext } from '../typing/context';
import { WorkflowFunctionOutput } from '../typing/workflow';
import { PipelineStorage } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { writeTableToStorage } from '../../utils/storage';

const logger = console;

/**
 * Load and parse input documents into a standard format.
 */
export async function runWorkflow(
    config: GraphRagConfig,
    context: PipelineRunContext
): Promise<WorkflowFunctionOutput> {
    const output = await loadInputDocuments(
        config.input,
        context.inputStorage
    );

    logger.info(`Final # of rows loaded: ${output.data.length}`);
    context.stats.numDocuments = output.data.length;

    await writeTableToStorage(output, "documents", context.outputStorage);

    return {
        result: output,
        stop: false
    };
}

/**
 * Load and parse input documents into a standard format.
 */
export async function loadInputDocuments(
    config: InputConfig, 
    storage: PipelineStorage
): Promise<DataFrame> {
    return await createInput(config, storage);
}
