﻿/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * All the steps to transform final community reports.
 */

import { v4 as uuidv4 } from 'uuid';
import { DataFrame } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { COMMUNITY_REPORTS_FINAL_COLUMNS } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            ;
import { join } from '../utils/dataframes';

/**
 * All the steps to transform final community reports.
 * @param reports - DataFrame containing community reports
 * @param communities - DataFrame containing communities data
 * @returns Finalized community reports DataFrame
 */
export function finalizeCommunityReports(
    reports: DataFrame,
    communities: DataFrame
): DataFrame {
    // Select specific columns from communities
    const communityFields = communities.data.map(row => ({
        community: row.community,
        parent: row.parent,
        children: row.children,
        size: row.size,
        period: row.period
    }));

    const communityFieldsDF: DataFrame = {
        columns: ['community', 'parent', 'children', 'size', 'period'],
        data: communityFields
    };

    // Merge with communities to add shared fields
    const communityReports = join(reports, communityFieldsDF, 'community', 'left');

    // Transform the data
    const finalData = communityReports.data.map((row, index) => ({
        ...row,
        community: parseInt(String(row.community), 10),
        human_readable_id: parseInt(String(row.community), 10),
        id: uuidv4().replace(/-/g, '') // Remove dashes to match Python uuid4().hex
    }));

    // Filter to final columns
    const filteredData = finalData.map(row => {
        const newRow: Record<string, any> = {};
        COMMUNITY_REPORTS_FINAL_COLUMNS.forEach(col => {
            if (col in row) {
                newRow[col] = row[col];
            }
        });
        return newRow;
    });

    return {
        columns: COMMUNITY_REPORTS_FINAL_COLUMNS,
        data: filteredData
    };
}
