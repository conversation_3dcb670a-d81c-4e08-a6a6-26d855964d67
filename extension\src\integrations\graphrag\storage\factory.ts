﻿/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 * 
 * Factory functions for creating storage.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { StorageType } from '../config/enums'
import { PipelineStorage } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            
import { createBlobStorage } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            
import { createCosmosDbStorage } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            
import { createFileStorage } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            
import { MemoryPipelineStorage } 
                param($match)
                $prefix = $match.Groups[1].Value
                $path = $match.Groups[2].Value  
                $suffix = $match.Groups[3].Value
                $newPath = $path -replace '-', '_'
                return $prefix + $newPath + $suffix
            

/**
 * Type for storage creator function.
 */
export type StorageCreator = (kwargs: Record<string, any>) => PipelineStorage

/**
 * A factory class for storage implementations.
 * 
 * Includes a method for users to register a custom storage implementation.
 * Configuration arguments are passed to each storage implementation as kwargs
 * for individual enforcement of required/optional arguments.
 */
export class StorageFactory {
    private static _storageRegistry: Map<string, StorageCreator> = new Map()
    
    // For backward compatibility
    public static storageTypes: Map<string, any> = new Map()

    /**
     * Register a custom storage implementation.
     */
    static register(storageType: string, creator: StorageCreator): void {
        this._storageRegistry.set(storageType, creator)
        
        // For backward compatibility with code that may access storage_types directly
        this.storageTypes.set(storageType, creator)
    }

    /**
     * Create a storage object from the provided type.
     */
    static createStorage(
        storageType: StorageType | string, 
        kwargs: Record<string, any> = {}
    ): PipelineStorage {
        const storageTypeStr = typeof storageType === 'string' 
            ? storageType 
            : storageType

        if (!this._storageRegistry.has(storageTypeStr)) {
            throw new Error(`Unknown storage type: ${storageType}`)
        }

        const creator = this._storageRegistry.get(storageTypeStr)!
        return creator(kwargs)
    }

    /**
     * Get the registered storage implementations.
     */
    static getStorageTypes(): string[] {
        return Array.from(this._storageRegistry.keys())
    }

    /**
     * Check if the given storage type is supported.
     */
    static isSupportedStorageType(storageType: string): boolean {
        return this._storageRegistry.has(storageType)
    }
}

// --- Register default implementations ---
StorageFactory.register(StorageType.BLOB, createBlobStorage)
StorageFactory.register(StorageType.COSMOSDB, createCosmosDbStorage)
StorageFactory.register(StorageType.FILE, createFileStorage)
StorageFactory.register(StorageType.MEMORY, () => new MemoryPipelineStorage())
