/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing the PipelineRunResult class.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { PipelineState } from './state.js';

/**
 * Pipeline run result class definition.
 * Matches the Python PipelineRunResult dataclass exactly.
 */
export interface PipelineRunResult {
    /** The name of the workflow that was executed */
    workflow: string;

    /** The result of the workflow function. This can be anything - we use it only for logging downstream, and expect each workflow function to write official outputs to the provided storage */
    result: any | null;

    /** Ongoing pipeline context state object */
    state: PipelineState;

    /** List of errors that occurred during execution */
    errors: BaseException[] | null;
}

// Python uses BaseException, but in TypeScript we use Error as the base exception type
export type BaseException = Error;