// Copyright (c) 2024 Microsoft Corporation.
// Licensed under the MIT License

/**
 * Indexing API for GraphRAG.
 * 
 * WARNING: This API is under development and may undergo changes in future releases.
 * Backwards compatibility is not guaranteed at this time.
 */

import { NoopWorkflowCallbacks } from '../callbacks/noop_workflow_callbacks';
import { WorkflowCallbacks } from '../callbacks/workflow_callbacks';
import { IndexingMethod } from '../config/enums';
import { GraphRagConfig } from '../config/models/graph_rag_config';
import { runPipeline } from '../index/run/run_pipeline';
import { createCallbackChain } from '../index/run/utils';
import { PipelineRunResult } from '../index/typing/pipeline_run_result';
import { WorkflowFunction } from '../index/typing/workflow';
import { PipelineFactory } from '../index/workflows/factory';
import { initLoggers } from '../logger/standard_logging';

/**
 * Run the pipeline with the given configuration.
 * 
 * @param config - The configuration.
 * @param method - Styling of indexing to perform (full LLM, NLP + LLM, etc.).
 * @param isUpdateRun - Whether this is an update run.
 * @param memoryProfile - Whether to enable memory profiling.
 * @param callbacks - A list of callbacks to register.
 * @returns The list of pipeline run results.
 */
export async function buildIndex(options: {
    config: GraphRagConfig;
    method?: IndexingMethod | string;
    isUpdateRun?: boolean;
    memoryProfile?: boolean;
    callbacks?: WorkflowCallbacks[] | null;
}): Promise<PipelineRunResult[]> {
    const {
        config,
        method = IndexingMethod.Standard,
        isUpdateRun = false,
        memoryProfile = false,
        callbacks = null
    } = options;

    initLoggers({ config });

    // Create callbacks for pipeline lifecycle events if provided
    const workflowCallbacks = callbacks 
        ? createCallbackChain(callbacks) 
        : new NoopWorkflowCallbacks();

    const outputs: PipelineRunResult[] = [];

    if (memoryProfile) {
        console.warn("New pipeline does not yet support memory profiling.");
    }

    console.info("Initializing indexing pipeline...");
    // todo: this could propagate out to the cli for better clarity, but will be a breaking api change
    const actualMethod = getMethod(method, isUpdateRun);
    const pipeline = PipelineFactory.createPipeline(config, actualMethod);

    workflowCallbacks.pipelineStart(pipeline.names());

    for await (const output of runPipeline(
        pipeline,
        config,
        workflowCallbacks,
        isUpdateRun
    )) {
        outputs.push(output);
        if (output.errors && output.errors.length > 0) {
            console.error(`Workflow ${output.workflow} completed with errors`);
        } else {
            console.info(`Workflow ${output.workflow} completed successfully`);
        }
        console.debug(JSON.stringify(output.result));
    }

    workflowCallbacks.pipelineEnd(outputs);
    return outputs;
}

/**
 * Register a custom workflow function. You can then include the name in the settings.yaml workflows list.
 * 
 * @param name - The name of the workflow.
 * @param workflow - The workflow function.
 */
export function registerWorkflowFunction(name: string, workflow: WorkflowFunction): void {
    PipelineFactory.register(name, workflow);
}

/**
 * Get the method string based on the method and update run flag.
 * 
 * @param method - The indexing method.
 * @param isUpdateRun - Whether this is an update run.
 * @returns The method string.
 */
function getMethod(method: IndexingMethod | string, isUpdateRun: boolean): string {
    const methodValue = typeof method === 'string' ? method : method.valueOf();
    return isUpdateRun ? `${methodValue}-update` : methodValue;
}
