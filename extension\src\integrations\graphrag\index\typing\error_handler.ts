/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Shared error handler types.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

/**
 * Error handler function type.
 * Matches the Python ErrorHandlerFn type exactly.
 */
export type ErrorHandlerFn = (
    error: BaseException | null,
    message: string | null,
    context: Record<string, any> | null
) => void;

// Python uses BaseException, but in TypeScript we use Error as the base exception type
// Adding a type alias for compatibility
export type BaseException = Error;